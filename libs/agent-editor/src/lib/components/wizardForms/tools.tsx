import React, { FC, useEffect, useState } from 'react';
import Box from '@atomic/box';
import { ApexTheme, DeviceTypes } from '@base/theme';
import { useTheme } from 'react-jss';
import Icon, { IconCodes } from '@atomic/icon';
import Button, { ButtonTypes, ButtonSizes, IconPosition } from '@atomic/button';
import Text, { TextSizes, TextTypes } from '@atomic/text';
import ToolCard from '../toolCard';
import openAIImage from '../../assets/openAI.png';
import Tab, { TabsTypes, TabVariant } from '@atomic/segmenttabs';
import TabNavigation, { Content } from '@atomic/tabnavigation';
import Masonry from 'react-layout-masonry';
import { useScrollbarStyles } from '@atomic/scrollbar';
import { deepClone, useGlobalState } from '@editors/core';
import { Dropdown, ModelRepoService } from '@composite/doceditorviewer';
import { useAgenticAiEditorState } from '../../state';
import { TooltipPosition } from '@atomic/tooltip';
import {
	buildOperationsArray,
	getBOList,
	getSelectedOperations,
	getServicesList,
	saveToForm,
	getSelectedServiceList,
	getDeselectedOperations
} from './helper';
import ToolsRightPanel from './toolsRightPanel';
import { OperationSelection } from './operationSelection';
import { useHookstate } from '@hookstate/core';
import { ToolsTypes } from './constants';
import { BOAttributeSelection } from './boToolSelection';
import { BOListingTypes, BOListingUtils } from '@editors/shared-components';
import Input from '@atomic/input';

export const Tools: FC = () => {
	const theme = useTheme<ApexTheme>();
	const scrollStyles = useScrollbarStyles();
	const { applicationName } = useGlobalState();
	const {
		appModelList,
		agentCreationForm: { tools },
		createAgentInfo,
		agentCreationForm,
	} = useAgenticAiEditorState();
	const [selectedApp, setSelectedApp] = useState<any>(
		deepClone(appModelList.value)[0].id
	);
	const [servicesList, setServicesList] = useState([]);
	const [allServiceList, setAllServiceList] = useState([]);
	const [selectedService, setSelectedService] = useState('');
	const [boList, setBOlist] = useState([]);
	const [serviceView, setServiceView] = useState('suggested');
	const [showModel, setShowModel] = useState({
		show: false,
		childrenType: 'apis',
		targetId: '',
		title: '',
		subText: '',
	});
	const [searchText, setSearchText] = useState<string>('');
	const operationList = useHookstate<Record<string, any>[]>([]);
	const nodeList = useHookstate<BOListingTypes.NodeType[]>([]);

	const handleAppNameChange = (appDetails: any) => {
		setSelectedApp(appDetails);
	};

	useEffect(() => {
		const fetchBOList = async () => {
			const boList = await getBOList(selectedApp);
			setBOlist(boList);
		};
		const fetchServicesList = async () => {
			const servicesList: any = await getServicesList(
				tools.value
			);

			const filteredServices = servicesList.filter((service: any) => {
				return service.isSuggested;
			});
			setServicesList(filteredServices);
			setAllServiceList(servicesList);
		};
		fetchServicesList();
		fetchBOList();
	}, [selectedApp]);

	const closeModel = () => {
		setShowModel({
			show: false,
			childrenType: 'apis',
			targetId: '',
			title: '',
			subText: '',
		});
	};

	const apiCardClick = async (data: any) => {
		const servicedId = data.id;
		setSelectedService(servicedId);
		setShowModel({
			show: true,
			childrenType: ToolsTypes.APIs,
			targetId: servicedId,
			title: data.label,
			subText: 'List of operations',
		});
		const opertaions = await buildOperationsArray(
			servicedId,
			tools,
			createAgentInfo
		);

		operationList.set(opertaions);
	};

	const boCardClick = async (data: any) => {
		const boId = data.id;
		setShowModel({
			show: true,
			childrenType: ToolsTypes.BusinessObjects,
			targetId: boId,
			title: data.name,
			subText: 'List of attributes',
		});
	};

	const handleFooterSaveCallback = () => {
		if (showModel.childrenType === ToolsTypes.APIs) {
			const selectedOperations = getSelectedOperations(
				deepClone(operationList.get())
			);
			const deselectedOperations = getDeselectedOperations(
				deepClone(operationList.get())
			);
			const finalToolsSaved = saveToForm(
				selectedOperations,
				deselectedOperations,
				tools,
				showModel.targetId,
				showModel.title
			);
			const selectedSerices = getSelectedServiceList(finalToolsSaved, allServiceList);
			setAllServiceList(selectedSerices)

		} else {
			const selectedFields = BOListingUtils.getSelectedFields(nodeList);
		}

		closeModel();
	};

	const handleViewButtonClick = () => {
		setSearchText('');
		const toggle = serviceView === 'suggested' ? 'all' : 'suggested';
		if (toggle === 'all') {
			setServicesList(allServiceList);
		} else {
			const filteredServices = allServiceList.filter((service: any) => {
				return service.isSuggested;
			});
			setServicesList(filteredServices);
		}
		setServiceView(toggle);
	};

	const handleAgentNameChange = (event: any) => {
		if (event.target.value === '') {
			const isSuggestedView = serviceView === 'suggested';
			if (isSuggestedView) {
				const filteredServices = allServiceList.filter((service: any) => {
					return service.isSuggested;
				});
				setServicesList(filteredServices);
			} else {
				setServicesList(allServiceList);
			}
			setSearchText('');
			return;
		}
		setSearchText(event.target.value);
		const filteredServices = servicesList.filter((service: any) => {
			return service.label
				.toLowerCase()
				.includes(event.target.value.toLowerCase());
		});
		setServicesList(filteredServices);
	};

	return (
		<Box height={'100%'} width={'100%'} gap={'16px'} flexDirection="column">
			<Box flexDirection="column" gap={'8px'}>
				<Text
					text="Suggested Tools"
					color={'#14142B'}
					fontWeight={theme.fontWeights.semiBold}
					fontSize={theme.fontSizes[DeviceTypes.Desktop].bMedium}
				/>
				<Box gap={'40px'}>
					<Text
						text="Based on the purpose of the agent that you have given, here is a list of tools which I think would be useful to you."
						color={theme.colors.monochrome.body}
						fontWeight={theme.fontWeights.regular}
						fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
					/>
				</Box>
			</Box>
			<TabNavigation
				tabsAndContentContainerProps={{ width: '100%' }}
				type={TabsTypes.ValueOnly}
				defaultSelectedTab={0}
				variant={TabVariant.Default}
				isOverflow={false}
				tabsContainerStyle={{
					padding: '0 5px',
					gap: '0px',
				}}
			>
				<Tab
					title="APIs"
					key="APIs"
					overflowLabel="APIs"
					id="apis"
					tabTextStyle={{ fontWeight: theme.fontWeights.semiBold }}
				>
					<Box flexDirection="column" gap="10px" width={'100%'}>
					<Box justifyContent='space-between'>
						<Box width="327px">
						<Input
							value={searchText}
							width="327px"
							onChange={handleAgentNameChange}
							label="Agent Name"
							showLabel={false}
							type="input"
							inputPlaceholderStyles={{
								fontWeight: theme.fontWeights.semiBold,
								fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
							}}
							inputPrefix={() => (
								<Icon
									icon={IconCodes.icon_Bd_Search}
									color={theme.colors.monochrome.label}
									fontSize={theme.fontSizes.desktop.bHuge}
									height={'20px'}
								/>
							)}
							placeholder="Search"
							name="labelInput"
							captionContainerStyles={{
								width: '327px',
							}}
							inputContainerProps={{
								marginBottom: '-5px',
								width: '327px',
							}}
							labelProps={{
								color: theme.colors.monochrome.body,
								fontWeight: theme.fontWeights.semiBold,
								fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
							}}
						/>
						</Box>
					</Box>
						<Box
							id={'apisTabContainer'}
							flexDirection="row"
							flexWrap="wrap"
							display="flex"
							gap={'24px'}
							marginTop={'10px'}
							justifyContent="flex-start"
							maxHeight={'calc(100vh - 355px'}
							className={scrollStyles.scrollbar}
						>
							{servicesList.map((api: any) => {
								return (
									<ToolCard
										data={api}
										footerText={`5 operations`}
										selected={api.id === showModel.targetId}
										icon={IconCodes.icon_Tb_api_app}
										title={api.label}
										description={
											`${api.label}`
										}
										onClickCb={apiCardClick}
										mainConatinerProps={{
											height: 'fit-content',
										}}
									/>
								);
							})}
						</Box>
						<Button
							title={`${
								serviceView === 'suggested' ? 'View all' : 'View suggested'
							}`}
							buttonType={ButtonTypes.Subtle}
							iconPosition={IconPosition.Right}
							backgroundColor={theme.colors.monochrome.white}
							borderRadius='25px'
							iconProps={{
								icon: IconCodes.icon_Tb_chevron_down,
								iconStyle: {
									transform: `rotate(${
										serviceView === 'suggested' ? '0deg' : '180deg'
									})`,
								},
							}}
							onClick={handleViewButtonClick}
							buttonStyles={{
								alignSelf: 'flex-start',
							}}
						></Button>
					</Box>
				</Tab>
				<Tab
					title="Business Objects"
					key="businessObjects"
					overflowLabel="Business Objects"
					tabTextStyle={{ fontWeight: theme.fontWeights.semiBold }}
				>
					<Box flexDirection='column'>
					<Box justifyContent='space-between'>
					<Input
							value={searchText}
							width="327px"
							onChange={handleAgentNameChange}
							label="Agent Name"
							showLabel={false}
							type="input"
							inputPlaceholderStyles={{
								fontWeight: theme.fontWeights.semiBold,
								fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
							}}
							inputPrefix={() => (
								<Icon
									icon={IconCodes.icon_Bd_Search}
									color={theme.colors.monochrome.label}
									fontSize={theme.fontSizes.desktop.bHuge}
									height={'20px'}
								/>
							)}
							placeholder="Search"
							name="labelInput"
							captionContainerStyles={{
								width: '327px',
							}}
							inputContainerProps={{
								marginBottom: '-5px',
								width: '327px',
								alignSelf: 'flex-end'
							}}
							labelProps={{
								color: theme.colors.monochrome.body,
								fontWeight: theme.fontWeights.semiBold,
								fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
							}}
						/>
					<Dropdown
							width="320px"
							label="Select"
							name="Select"
							dropdownLabelStyles={{ color: theme.colors.monochrome.label }}
							dropdownContainerStyle={{ id: 'appSelect', alignSelf: 'end' }}
							selectedValues={selectedApp}
							data={deepClone(appModelList.value)}
							tooltipPossiblePlacements={[TooltipPosition.BottomStart]}
							onItemClick={handleAppNameChange}
							scrollbarStyles={{
								maxHeight: '200px',
							}}
							makeSelectionClearable={false}
							containerStyles={{ width: '100%' }}
							foregroundColor={theme.colors.monochrome.white}
							dropdownStyle={{
								border: `2px solid ${theme.colors.monochrome.line}`,
							}}
							disabledStyles={{
								backgroundColor: theme.colors.monochrome.white,
								opacity: 0.5,
							}}
						/>
					</Box>
					<Box
						width={'100%'}
						marginTop={'10px'}
						height={'calc(100vh - 300px'}
						className={scrollStyles.scrollbar}
					>
						<Masonry
							columns={{ 640: 1, 768: 1, 1024: 2, 1280: 3, 1536: 4 }}
							gap={24}
						>
							{boList.map((bo: any) => {
								return (
									<ToolCard
										data={bo}
										title={bo.name}
										icon={bo.icon}
										selected={bo.id === showModel.targetId}
										description={bo.description}
										onClickCb={boCardClick}
										footerText={`${bo.attributesCount} attributes`}
										mainConatinerProps={{
											height: 'fit-content',
											display: 'flex',
										}}
										descriptionTextProps={{
											height: 'fit-content',
											backgroundColor: theme.colors.monochrome.white,
											padding: '0px 0px',
										}}
									/>
								);
							})}
						</Masonry>
					</Box>
					</Box>
				</Tab>
			</TabNavigation>
			{ToolsTypes.APIs === showModel.childrenType ? (
				<ToolsRightPanel
					showModel={showModel.show}
					onModalClose={closeModel}
					title={showModel.title}
					subText={showModel.subText}
					saveCallback={handleFooterSaveCallback}
				>
					<OperationSelection
						targetId={showModel.targetId}
						operation={operationList}
					/>
				</ToolsRightPanel>
			) : (
				<ToolsRightPanel
					showModel={showModel.show}
					onModalClose={closeModel}
					title={showModel.title}
					subText={showModel.subText}
					saveCallback={handleFooterSaveCallback}
				>
					<BOAttributeSelection
						nodeList={nodeList}
						boSchemaId={showModel.targetId}
					/>
				</ToolsRightPanel>
			)}
		</Box>
	);
};
