import { TextSizes } from '@atomic/text';
import Modal, { DockType, WindowSize } from '@composite/modal';
import Icon, { Animations, IconCodes } from '@atomic/icon';
import { useTheme } from 'react-jss';
import { ApexTheme } from '@base/theme';
import { useStyles } from './styles';
import Box from '@atomic/box';
import Info, { InfoTypes } from '@atomic/info';
import Tab, { TabsTypes, TabVariant } from '@atomic/segmenttabs';
import TabNavigation, { Content } from '@atomic/tabnavigation';
import Text from '@atomic/text';
import { RunLog } from './runLog';
import { data } from '../../data';
import { ConversationLogs } from './conversationLogs';

export interface AgentExecutionDetailsProps {
	onModalClose: () => void;
	currentRowExecutionDetails: Record<string, any>;
}

export const getRunTimeFromDuration = (duration: any) => {
	if (Math.sign(duration) === -1) return '--:--:--';
	const difference = Math.floor(duration / 1000);
	const hoursDifference = Math.floor(difference / 3600) % 24;
	const minutesDifference = Math.floor(difference / 60) % 60;
	const secondsDifference = Math.floor(difference % 60);
	const milliSecondsDifference = Math.floor(duration % 1000);
	return `${hoursDifference ? `${hoursDifference} hrs` : ''}
    ${minutesDifference ? `${minutesDifference} mins` : ''}
    ${
			secondsDifference
				? `${secondsDifference} sec`
				: `${milliSecondsDifference ? `${milliSecondsDifference} ms` : '0 ms'}`
		}
    `;
};

export const AgentExecutionDetails: React.FC<AgentExecutionDetailsProps> = ({
	onModalClose,
	currentRowExecutionDetails,
}): JSX.Element => {
	const theme: ApexTheme = useTheme();
	const classes = useStyles();

	const { started_at, time_ms, has_error, has_error_child } = currentRowExecutionDetails;
	const hasError = has_error || has_error_child;

	const buttonStyles = {
		width: '40px',
		height: '40px',
		padding: 0,
		minWidth: 'fit-content',
		backgroundColor: 'transparent',
	};

	const buttonBarStyle = {
		padding: '8px 10px',
	};

	const tabStyleProps = (theme: ApexTheme) => ({
		fontWeight: theme.fontWeights.XBold,
		color: theme.colors.monochrome.label,
		padding: '8px 4px',
	});

	const getIconProps = () => {
		return hasError
			? {
					icon: IconCodes.icon_Bd_Filled_Success,
					color: theme.colors.success.default,
			  }
			: {
					icon: IconCodes.icon_Bd_Filled_Close,
					color: theme.colors.red[500],
			  };
	};

	const getHeaderComponent = () => {
		return (
			<Box justifyContent="space-between">
				<Text
					fontSize={theme.fontSizes.desktop.bLarge}
					fontWeight={theme.fontWeights.semiBold}
					lineHeight={theme.lineHeights.desktop.bHuge}
					color={theme.colors.monochrome.body}
				>
					Execution details
				</Text>
				<Box
					gap="4px"
					alignItems="center"
					position="absolute"
					right={'80px'}
					top="23px"
				>
					{/* <Icon
						{...getIconProps()}
						fontSize={theme.fontSizes.desktop.hXSmall}
					/> */}
					{/* <Text
						color={theme.colors.monochrome.body}
						fontSize={theme.fontSizes.desktop.bSmall}
					>
						{hasError ? 'Success' : 'Failed'}
					</Text> */}
				</Box>
			</Box>
		);
	};

	return (
		<Modal
			showModal={true}
			isButtonBarEnabled={false}
			isTitleBarEnabled={true}
			windowSize={WindowSize.Medium}
			headerProps={{
				title: 'Execution details',
				titleStyles: {
					size: TextSizes.Medium,
				},
				tooltipProps: { isOpen: false },
			}}
			isDefaultModal
			headerComponentRender={getHeaderComponent}
			headerButtonArray={[
				{
					buttonStyles,
					iconProps: {
						icon: IconCodes.icon_Bd_Close_X,
						height: '18px',
						fontSize: theme.fontSizes.desktop.bMedium,
					},
				},
			]}
			closeClickHandler={onModalClose}
			headerButtonBarProps={{
				buttonBarProps: buttonBarStyle,
			}}
			headerLeftButtonArray={[
				{
					iconProps: {
						icon: IconCodes.icon_Bd_Back_Arrow,
						onClick: onModalClose,
						fontSize: theme.fontSizes.desktop.bMedium,
						color: theme.colors.monochrome.label,
					},
				},
			]}
			dockType={DockType.Right}
			modalStyle="Modern"
			windowContainerProps={{ zIndex: 12 }}
			className={`${classes.windowContainerStyles} ${classes.popup} ${
				true ? classes.show : ''
			}`}
		>
			<Box flexDirection="column" width="100%" position='relative'>
				<Box
					justifyContent={'space-between'}
					width="100%"
					backgroundColor={theme.colors.monochrome.white}
					padding="10px"
				>
					<Box padding="10px">
						<Info
							label="Run date and time"
							value={new Date(currentRowExecutionDetails?.['started_at']).toString().substring(4, 24)}
							type={InfoTypes.TEXT}
							name={''}
						></Info>
					</Box>
					{/* <Box padding="10px">
						<Info
							label="Total time"
							value={getRunTimeFromDuration(time_ms)}
							type={InfoTypes.TEXT}
							name={''}
						></Info>
					</Box> */}
				</Box>
				{data.spans.length > 1 ? (
					<ConversationLogs data={currentRowExecutionDetails} />
				) : (
					<TabNavigation
						tabsAndContentContainerProps={{ width: '100%' }}
						type={TabsTypes.ValueOnly}
						defaultSelectedTab={0}
						variant={TabVariant.Default}
						isOverflow={false}
						tabsContainerStyle={{
							padding: '0 5px',
							gap: '0px',
						}}
					>
						<Tab
							title="Run logs"
							key="RunLogs"
							overflowLabel="Run logs"
							tabStyleProps={tabStyleProps(theme)}
							tabTextStyle={{ fontWeight: theme.fontWeights.semiBold }}
						>
							<RunLog span={currentRowExecutionDetails?.['spans']} />
						</Tab>
					</TabNavigation>
				)}
			</Box>
		</Modal>
	);
};
