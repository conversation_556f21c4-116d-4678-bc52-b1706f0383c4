import { FC } from 'react';
import Box from '@atomic/box';
import Image from '@atomic/image';
import Text, { TextTypes } from '@atomic/text';
import Tooltip, { TooltipType, TooltipPosition } from '@atomic/tooltip';
import Tag, { TagSize, TagStyle, TagType } from '@atomic/tags';
import { useTheme } from 'react-jss';
import { ApexTheme, DeviceTypes } from '@base/theme';
import Icon, { IconCodes, Animations } from '@atomic/icon';
import { ToolCardProps } from './types';
import { usePreviewCardStyles } from './styles';
import { TextWithTooltip } from '@composite/doceditorviewer';

const ToolCard: FC<ToolCardProps> = ({
	image,
	title,
	icon,
	description,
	footerText,
	mainConatinerProps,
	descriptionTextProps,
	data,
	onClickCb,
	selected,
}) => {
	const theme: ApexTheme = useTheme();
	const classes = usePreviewCardStyles();

	const handleOnClick = () => {
		onClickCb?.(data);
	};

	return (
		<Box
			flexDirection="column"
			padding="16px"
			backgroundColor={theme.colors.monochrome.white}
			border={
				selected
					? `2px solid ${theme.colors.primary[700]}`
					: `1px solid ${theme.colors.monochrome.line}`
			}
			hoverProps={{
				border: `2px solid ${theme.colors.primary[700]}`,
			}}
			borderRadius="8px"
			width="329px"
			height="242px"
			gap={'15px'}
			onClick={handleOnClick}
			{...mainConatinerProps}
		>
			<Box flexDirection="row" alignItems="center" width={'100%'}>
				{image ? (
					<Image
						src={image}
						borderRadius="8px"
						width="48px"
						height="48px"
						styleProps={{
							objectFit: 'cover',
							alignSelf: 'center',
							background: theme.colors.monochrome.bg,
						}}
						containerBoxProps={{
							width: '48px',
							border: `1px solid ${theme.colors.monochrome.line}`,
							borderRadius: '8px',
						}}
					/>
				) : (
					<Icon
						width={'44px'}
						height={'44px'}
						fontSize={theme.fontSizes[DeviceTypes.Desktop].hSmall}
						icon={icon}
						color={theme.colors.primary[500]}
						borderRadius="40px"
						backgroundColor={theme.colors.primary.bg}
						padding="8px"
						altName="player"
						iconStyle={{
							cursor: 'pointer',
							width: '44px',
							height: '44px',
						}}
					/>
				)}
				<TextWithTooltip
					toolTipProps={{
						placement: TooltipPosition.TopCenter,
						triggerOffset: 10,
						type: TooltipType.Tooltip,
						textStyles: { whiteSpace: 'break-spaces', wordBreak: 'unset' },
					}}
					textProps={{
						textOverflow: 'ellipsis',
						maxWidth: '200px',
						overflow: 'hidden',
						whiteSpace: 'nowrap',
						fontSize: theme.fontSizes.mobile.bSmall,
						color: theme.colors.monochrome.ash,
						type: TextTypes.Body,
						fontWeight: theme.fontWeights.bold,
						marginLeft: '10px',
					}}
				>
					{title}
				</TextWithTooltip>
			</Box>
			{description && (
				<Text
					text={description}
					color={theme.colors.monochrome.ash}
					fontWeight={theme.fontWeights.regular}
					fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
					backgroundColor={theme.colors.monochrome.bg}
					height={'106px'}
					padding={'9px 16px'}
					borderRadius={'8px'}
					{...descriptionTextProps}
				/>
			)}
			<Text
				text={footerText}
				color={theme.colors.monochrome.body}
				fontWeight={theme.fontWeights.semiBold}
				fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
			/>
		</Box>
	);
};

export default ToolCard;
