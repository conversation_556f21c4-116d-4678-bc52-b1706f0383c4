import { TextSizes, TextTypes } from '@atomic/text';
import Modal, { DockType, WindowSize } from '@composite/modal';
import Icon, { Animations, IconCodes } from '@atomic/icon';
import { useTheme } from 'react-jss';
import { ApexTheme, DeviceTypes } from '@base/theme';
import { useStyles } from './styles';
import Box from '@atomic/box';
import Info, { InfoTypes } from '@atomic/info';
import Tab, { TabsTypes, TabVariant } from '@atomic/segmenttabs';
import TabNavigation, { Content } from '@atomic/tabnavigation';
import Text from '@atomic/text';
import Input from '@atomic/input';
import { useEffect, useState } from 'react';
import Dropdown from '@atomic/dropdown';
import { useAgenticAiEditorState } from '../state';
import { deepClone } from '@editors/core';
import { TooltipPosition } from '@atomic/tooltip';
import { useScrollbarStyles } from '@atomic/scrollbar';
import Masonry from 'react-layout-masonry';
import ToolCard from './toolCard';
import { getBOList, getServicesList } from './wizardForms/helper';
import { ToolsTypes } from './wizardForms/constants';
import ToolsRightPanel from './wizardForms/toolsRightPanel';
import BOAttributeSelection from './wizardForms/boToolSelection';
import { useHookstate } from '@hookstate/core';
import { BOListingTypes, BOListingUtils } from '@editors/shared-components';

export interface NonConversationalWindowProps {
	onModalClose: () => void;
}

export const NonConversationalWindow: React.FC<
	NonConversationalWindowProps
> = ({ onModalClose }): JSX.Element => {
	const theme: ApexTheme = useTheme();
	const {
		appModelList,
		agentCreationForm: { tools },
		createAgentInfo,
		agentCreationForm,
	} = useAgenticAiEditorState();
	const [selectedApp, setSelectedApp] = useState<any>(
		deepClone(appModelList.value)[0].id
	);
	const [boList, setBOlist] = useState([]);
	const [initialBOList, setInitialBOList] = useState([]);
	const classes = useStyles();
	const scrollStyles = useScrollbarStyles();
	const nodeList = useHookstate<BOListingTypes.NodeType[]>([]);
	const [searchText, setSearchText] = useState<string>('');
	const [showModel, setShowModel] = useState({
		show: false,
		childrenType: 'apis',
		targetId: '',
		title: '',
		subText: '',
	});

	useEffect(() => {
		const fetchBOList = async () => {
			const boList = await getBOList(selectedApp);
			setBOlist(boList);
			setInitialBOList(boList)
		};
		// const fetchServicesList = async () => {
		// 	const servicesList: any = await getServicesList(
		// 		tools.value
		// 	);

		// 	const filteredServices = servicesList.filter((service: any) => {
		// 		return service.isSuggested;
		// 	});
		// 	setServicesList(filteredServices);
		// 	setAllServiceList(servicesList);
		// };
		// fetchServicesList();
		fetchBOList();
	}, [selectedApp]);

	const buttonStyles = {
		width: '40px',
		height: '40px',
		padding: 0,
		minWidth: 'fit-content',
		backgroundColor: 'transparent',
	};

	const buttonBarStyle = {
		padding: '8px 10px',
	};

	const closeModel = () => {
		setShowModel({
			show: false,
			childrenType: 'apis',
			targetId: '',
			title: '',
			subText: '',
		});
	};

	const getHeaderComponent = () => {
		return (
			<Box justifyContent="space-between" flexDirection="column">
				<Text
					fontSize={theme.fontSizes.desktop.bMedium}
					fontWeight={theme.fontWeights.semiBold}
					lineHeight={theme.lineHeights.desktop.bHuge}
					color={'#14142B'}
				>
					Application Objects
				</Text>
				<Text
					text="Select a business object and the attributes"
					color={theme.colors.monochrome.body}
					fontWeight={theme.fontWeights.regular}
					fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
				/>
			</Box>
		);
	};

	const handleSearchText = (event: any) => {
		if (event.target.value === '') {
			setBOlist(initialBOList);
			setSearchText('');
			return;
		}
		setSearchText(event.target.value);
		const filteredBOs = boList.filter((bo: any) => {
			return bo.name
				.toLowerCase()
				.includes(event.target.value.toLowerCase());
		});
		setBOlist(filteredBOs);
	};

	const handleAppNameChange = (appDetails: any) => {
		setSelectedApp(appDetails);
	};

	const boCardClick = async (data: any) => {
		const boId = data.id;
		setShowModel({
			show: true,
			childrenType: ToolsTypes.BusinessObjects,
			targetId: boId,
			title: data.name,
			subText: 'List of attributes',
		});
	};

	const handleFooterSaveCallback = () => {};

	return (
		<Modal
			showModal={true}
			isButtonBarEnabled={false}
			isTitleBarEnabled={true}
			windowSize={WindowSize.Medium}
			headerProps={{
				title: 'Execution details',
				titleStyles: {
					size: TextSizes.Medium,
				},
				tooltipProps: { isOpen: false },
			}}
			isDefaultModal
			headerComponentRender={getHeaderComponent}
			headerButtonArray={[
				{
					buttonStyles,
					iconProps: {
						icon: IconCodes.icon_Bd_Close_X,
						height: '18px',
						fontSize: theme.fontSizes.desktop.bMedium,
					},
				},
			]}
			closeClickHandler={onModalClose}
			headerButtonBarProps={{
				buttonBarProps: buttonBarStyle,
			}}
			dockType={DockType.Right}
			modalStyle="Modern"
			windowContainerProps={{ zIndex: 9999 }}
			className={`${classes.windowContainerStyles} ${classes.popup} ${
				true ? classes.show : ''
			}`}
		>
			<Box flexDirection="column" width="100%" position="relative" padding={'25px 80px 0px 25px'}>
				<Box flexDirection="column">
					<Box justifyContent="space-between">
						<Input
							value={searchText}
							width="327px"
							onChange={handleSearchText}
							label="Agent Name"
							showLabel={false}
							type="input"
							inputPlaceholderStyles={{
								fontWeight: theme.fontWeights.semiBold,
								fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
							}}
							inputPrefix={() => (
								<Icon
									icon={IconCodes.icon_Bd_Search}
									color={theme.colors.monochrome.label}
									fontSize={theme.fontSizes.desktop.bHuge}
									height={'20px'}
								/>
							)}
							placeholder="Search"
							name="labelInput"
							captionContainerStyles={{
								width: '327px',
							}}
							inputContainerProps={{
								marginBottom: '-5px',
								width: '327px',
								alignSelf: 'flex-end',
							}}
							labelProps={{
								color: theme.colors.monochrome.body,
								fontWeight: theme.fontWeights.semiBold,
								fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
							}}
						/>
						<Box height={'fit-content'}>
						<Dropdown
							width="320px"
							label="Select"
							name="Select"
							dropdownLabelStyles={{ color: theme.colors.monochrome.label }}
							dropdownContainerStyle={{
								id: 'appSelect',
								alignSelf: 'end',
								display: 'flex',
							}}
							selectedValues={selectedApp}
							data={deepClone(appModelList.value)}
							tooltipPossiblePlacements={[TooltipPosition.BottomStart]}
							onItemClick={handleAppNameChange}
							scrollbarStyles={{
								maxHeight: '200px',
							}}
							makeSelectionClearable={false}
							containerStyles={{ width: '100%', display: 'flex', alignSelf: 'flex-end' }}
							foregroundColor={theme.colors.monochrome.white}
							dropdownStyle={{
								border: `2px solid ${theme.colors.monochrome.line}`,
							}}
							disabledStyles={{
								backgroundColor: theme.colors.monochrome.white,
								opacity: 0.5,
							}}
						/>
						</Box>
					</Box>
					<Box
						width={'100%'}
						marginTop={'10px'}
						height={'calc(100vh - 140px'}
						className={scrollStyles.scrollbar}
					>
						<Masonry
							columns={{ 640: 1, 768: 2, 1024: 3, 1280: 4, 1536: 5 }}
							gap={24}
						>
							{boList.map((bo: any) => {
								return (
									<ToolCard
										data={bo}
										title={bo.name}
										icon={bo.icon}
										selected={bo.id === showModel.targetId}
										description={bo.description}
										onClickCb={boCardClick}
										footerText={`${bo.attributesCount} attributes`}
										mainConatinerProps={{
											height: 'fit-content',
											display: 'flex',
										}}
										descriptionTextProps={{
											height: 'fit-content',
											backgroundColor: theme.colors.monochrome.white,
											padding: '0px 0px',
										}}
									/>
								);
							})}
						</Masonry>
					</Box>
				</Box>
			</Box>
			{
				<ToolsRightPanel
					showModel={showModel.show}
					onModalClose={closeModel}
					title={showModel.title}
					subText={showModel.subText}
					saveCallback={handleFooterSaveCallback}
				>
					<BOAttributeSelection
						nodeList={nodeList}
						boSchemaId={showModel.targetId}
					/>
				</ToolsRightPanel>
			}
		</Modal>
	);
};
