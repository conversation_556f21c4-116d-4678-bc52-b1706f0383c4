import { FC, useEffect, useState } from 'react';
import Box from '@atomic/box';
import { ApexTheme, DeviceTypes } from '@base/theme';
import { useTheme } from 'react-jss';
import Icon, { IconCodes } from '@atomic/icon';
import Button, { ButtonTypes, ButtonSizes } from '@atomic/button';
import Text, { TextSizes, TextTypes } from '@atomic/text';
import Input from '@atomic/input';
import Textarea from '@atomic/textarea';
import { ContextmenuType } from '@composite/contextmenu';
import { Dropdown } from '@composite/doceditorviewer';
import { SelectionCard } from '../selectionCard';
import Image from '@atomic/image';
import openAIImage from '../../assets/openAI.png';
import geminiImage from '../../assets/gemini.png';
import { useAgenticAiEditorState } from '../../state';
import { deepClone, useGlobalState } from '@editors/core';
import { loadBO } from './helper';
import AgentCard from '../agentCard';
import { useScrollbarStyles } from '@atomic/scrollbar';
import { NonConversationalWindow } from '../nonConversationalWindow'

export const WizardFormStep1: FC = () => {
	const theme = useTheme<ApexTheme>();
	const { activePage, agentCreationForm, appModelList, personaList } =
		useAgenticAiEditorState();
	const { applicationName } = useGlobalState();
	const { name, description, agentType, channel, modelName } =
		agentCreationForm;
	const [showNonConversationalWindow, setShowNonConversationalWindow] = useState(false);
	const personas = deepClone(personaList.get());
	const scrollStyles = useScrollbarStyles();

	useEffect(() => {
		const loadAppList = async () => {
			const appList = await loadBO(applicationName.value);
			appModelList.set(appList);
		};
		loadAppList();
	}, [applicationName]);

	const handleSelectionCardClick = (key: string, value: string) => {
		agentCreationForm.set({
			...deepClone(agentCreationForm.get()),
			[key]: value,
		});
	};

	const handleNonConversationalWindow = () => {
		setShowNonConversationalWindow(!showNonConversationalWindow)
	}

	return (
		<Box
			height={'calc(100vh - 130px'}
			width={'100%'}
			display="flex"
			flexDirection="column"
			gap={'24px'}
			className={scrollStyles.scrollbar}
		>
			<Text
				text="Let’s Start with the Basic Information"
				type={TextTypes.Body}
				fontSize={theme.fontSizes[DeviceTypes.Desktop].bMedium}
				color={'#14142B'}
				textStyles={{
					height: '40px',
				}}
				containIntrinsicHeight={'40px'}
				fontWeight={theme.fontWeights.semiBold}
			/>
			<Input
				onChange={(event: any) => {
					name.set(event.target.value);
				}}
				label="Agent Name"
				showLabel={true}
				type="input"
				placeholder="Type here..."
				name="labelInput"
				inputContainerProps={{
					marginBottom: '-5px',
				}}
				value={name.value}
				error={'jucucue'}
				labelProps={{
					color: theme.colors.monochrome.body,
					fontWeight: theme.fontWeights.semiBold,
					fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
				}}
			/>
			<Textarea
				name="Description"
				id="description"
				textareaWrapperProps={{ marginTop: '-15px' }}
				hasSecondaryIcon={false}
				isClearable={false}
				placeholder="Type here..."
				label="Purpose of the Agent"
				textareaProps={{ height: '80px' }}
				onChange={(event: any) => {
					description.set(event.target.value);
				}}
				readOnly={false}
				value={description.value}
				withLabel={true}
				labelTextProps={{
					color: theme.colors.monochrome.body,
					fontWeight: theme.fontWeights.semiBold,
					fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
					textStyles: {
						paddingBottom: '15px',
					},
				}}
			/>
			<Box flexDirection="column" gap={'8px'}>
				<Text
					text="Is this a conversational agent?"
					type={TextTypes.Body}
					fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
					color={theme.colors.monochrome.label}
					fontWeight={theme.fontWeights.semiBold}
				/>
				<Box display="flex" flexDirection="row" gap={'16px'}>
					<SelectionCard
						selected={agentType.value === 'AT_CONVERSATIONAL'}
						onCardClick={handleSelectionCardClick}
						name="agentType"
						value="AT_CONVERSATIONAL"
					>
						<Icon
							icon={IconCodes.icon_Tb_circle_check}
							iconStyle={{ marginRight: '5px' }}
						/>
						<Text
							text="Yes"
							type={TextTypes.Body}
							fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
							color={'unset'}
							fontWeight={theme.fontWeights.semiBold}
						/>
					</SelectionCard>
					<SelectionCard
						selected={agentType.value === 'AT_API'}
						onCardClick={handleSelectionCardClick}
						name="agentType"
						value="AT_API"
					>
						<Icon
							icon={IconCodes.icon_Tb_circle_x}
							iconStyle={{ marginRight: '5px' }}
						/>
						<Text
							text="No"
							type={TextTypes.Body}
							fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
							color={'unset'}
							fontWeight={theme.fontWeights.semiBold}
						/>
					</SelectionCard>
				</Box>
			</Box>
			<Box flexDirection="column" gap={'8px'}>
				<Text
					text="Communication Channel"
					type={TextTypes.Body}
					fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
					color={theme.colors.monochrome.label}
					fontWeight={theme.fontWeights.semiBold}
				/>
				<Box display="flex" flexDirection="row" gap={'16px'} flexWrap="wrap">
					<SelectionCard
						selected={channel.value === 'AC_INAPP'}
						onCardClick={handleSelectionCardClick}
						name="channel"
						value="AC_INAPP"
						hieght={'62px'}
					>
						<Icon
							icon={IconCodes.icon_Tb_message_circle}
							iconStyle={{ marginRight: '5px' }}
						/>
						<Text
							text="Chat"
							type={TextTypes.Body}
							fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
							color={'unset'}
							fontWeight={theme.fontWeights.semiBold}
						/>
					</SelectionCard>
					<SelectionCard
						selected={channel.value === 'AC_EMAIL'}
						onCardClick={handleSelectionCardClick}
						name="channel"
						value="AC_EMAIL"
						hieght={'62px'}
					>
						<Icon
							icon={IconCodes.icon_Tb_mail}
							iconStyle={{ marginRight: '5px' }}
						/>
						<Text
							text="Email"
							type={TextTypes.Body}
							fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
							color={'unset'}
							fontWeight={theme.fontWeights.semiBold}
						/>
					</SelectionCard>
					<SelectionCard
						selected={channel.value === 'AC_VOICE_APP'}
						onCardClick={handleSelectionCardClick}
						name="channel"
						value="AC_VOICE_APP"
						hieght={'62px'}
						disabled={true}
					>
						<Box flexDirection="column" alignItems="center">
							<Box flexDirection="row">
								<Icon
									icon={IconCodes.icon_Tb_circuit_battery}
									iconStyle={{ marginRight: '5px' }}
								/>
								<Text
									text="Voice on App"
									type={TextTypes.Body}
									fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
									color={'unset'}
									fontWeight={theme.fontWeights.semiBold}
								/>
							</Box>
							<Text
								text="(Coming Soon)"
								type={TextTypes.Body}
								fontSize={theme.fontSizes[DeviceTypes.Desktop].bXSmall}
								color={'unset'}
								fontWeight={theme.fontWeights.regular}
							/>
						</Box>
					</SelectionCard>
					<SelectionCard
						selected={channel.value === 'AC_VOICE_PHONE'}
						onCardClick={handleSelectionCardClick}
						name="channel"
						value="AC_VOICE_PHONE"
						hieght={'62px'}
						disabled={true}
					>
						<Box flexDirection="column" alignItems="center">
							<Box flexDirection="row">
								<Icon
									icon={IconCodes.icon_Tb_circuit_battery}
									iconStyle={{ marginRight: '5px' }}
								/>
								<Text
									text="Voice on Phone"
									type={TextTypes.Body}
									fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
									color={'unset'}
									fontWeight={theme.fontWeights.semiBold}
								/>
							</Box>
							<Text
								text="(Coming Soon)"
								type={TextTypes.Body}
								fontSize={theme.fontSizes[DeviceTypes.Desktop].bXSmall}
								color={'unset'}
								fontWeight={theme.fontWeights.regular}
							/>
						</Box>
					</SelectionCard>
				</Box>
			</Box>
			<Box flexDirection="column" gap={'8px'}>
				<Text
					text="Select LLM Model"
					type={TextTypes.Body}
					fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
					color={theme.colors.monochrome.label}
					fontWeight={theme.fontWeights.semiBold}
				/>
				<Box display="flex" flexDirection="row" gap={'16px'}>
					<SelectionCard
						disabled={true}
						selected={modelName.value === 'gpt-4o-mini'}
						hieght="62px"
						onCardClick={handleSelectionCardClick}
						name="modelName"
						value="gpt-4o-mini"
					>
						<Box display="flex" flexDirection="column" height={'42px'}>
							<Image src={openAIImage} height={'24px'} />
							<Text
								text="(Coming Soon)"
								type={TextTypes.Body}
								fontSize={theme.fontSizes[DeviceTypes.Desktop].bXSmall}
								color={'unset'}
								fontWeight={theme.fontWeights.regular}
							/>
						</Box>
					</SelectionCard>
					<SelectionCard
						hieght="62px"
						selected={modelName.value === 'gemini-2.0-flash'}
						onCardClick={handleSelectionCardClick}
						name="modelName"
						value="gemini-2.0-flash"
					>
						<Box display="flex" flexDirection="column" height={'24px'}>
							<Image src={geminiImage} height={'24px'} />
						</Box>
					</SelectionCard>
				</Box>
			</Box>
			{agentType.value === 'AT_API' && (
				<Box flexDirection="column" gap={'8px'}>
					<Text
						text="Select an Application Object and Attributes"
						type={TextTypes.Body}
						fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
						color={theme.colors.monochrome.label}
						fontWeight={theme.fontWeights.semiBold}
					/>
					<Box display="flex" flexDirection="row" gap={'16px'} flexWrap="wrap">
						<SelectionCard
							// selected={channel.value === 'AC_INAPP'}
							onCardClick={handleNonConversationalWindow}
							name="channel"
							value="AC_INAPP"
							hieght={'62px'}
						>
							<Icon
								icon={IconCodes.icon_Tb_list}
								iconStyle={{ marginRight: '5px' }}
							/>
							<Text
								text="Application Objects"
								type={TextTypes.Body}
								fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
								color={'unset'}
								fontWeight={theme.fontWeights.semiBold}
							/>
						</SelectionCard>
					</Box>
				</Box>
			)}
			{showNonConversationalWindow && (
				<NonConversationalWindow onModalClose={handleNonConversationalWindow} />
			)}
		</Box>
	);
};
