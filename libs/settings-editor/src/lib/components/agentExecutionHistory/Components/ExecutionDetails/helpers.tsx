import { ApexTheme } from '@base/theme';
import { IconCodes } from '@atomic/icon';

export const getRunTimeFromDuration = (duration: number) => {
	if (Math.sign(duration) === -1) return '--:--:--';
	const difference = Math.floor(duration / 1000);
	const hoursDifference = Math.floor(difference / 3600) % 24;
	const minutesDifference = Math.floor(difference / 60) % 60;
	const secondsDifference = Math.floor(difference % 60);
	const milliSecondsDifference = Math.floor(duration % 1000);
	return `${hoursDifference ? `${hoursDifference} hrs` : ''}
    ${minutesDifference ? `${minutesDifference} mins` : ''}
    ${
			secondsDifference
				? `${secondsDifference} sec`
				: `${milliSecondsDifference ? `${milliSecondsDifference} ms` : '0 ms'}`
		}
    `;
};

export const getJsonValue = (value: string) => {
	try {
		return JSON.parse(value);
	} catch (e) {
		return value;
	}
};

export const mapKeysToFilter = (obj: any, filterKeyMap: any) => {
	const mappedResult: any = {};
	for (const [key, value] of Object.entries(obj)) {
		const newKey = filterKeyMap[key] || key; // Use the mapped key or keep the original if not found
		mappedResult[newKey] = value;
	}
	return mappedResult;
};

export const joinQueryParams = (obj: any) => {
	const queryParams = [];

	for (const [key, value] of Object.entries(obj)) {
		queryParams.push(`${key}=${encodeURIComponent(String(value))}`);
	}

	return queryParams.join('&');
};

export const isJsonParsable = (str: string) => {
	try {
		JSON.parse(str);
		return true;
	} catch (e) {
		return false;
	}
};
export const formatDate = (isoString: string, opts?: any) => {
	if (!isoString) return '';
	const date = new Date(isoString);
	const options = {
		month: 'short',
		day: '2-digit',
		year: 'numeric',
		hour: '2-digit',
		minute: '2-digit',
		second: '2-digit',
		hour12: false,
		...opts,
	};

	return date.toLocaleString('en-US', options).split(',').join('');
};
