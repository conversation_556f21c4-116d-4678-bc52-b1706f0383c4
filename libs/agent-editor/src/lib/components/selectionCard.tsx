import { FC, useEffect, useState } from 'react';
import Box from '@atomic/box';
import { useTheme } from 'react-jss';
import { ApexTheme } from '@base/theme';

export interface SelectionCardProps {
	children: React.ReactNode;
	selected?: Boolean;
	hieght?: string;
	onCardClick?: (key: string, value: string) => void;
	name: string;
	value: string;
	disabled?: boolean;
}

export const SelectionCard: FC<SelectionCardProps> = ({
	children,
	selected,
	hieght,
	onCardClick,
	name,
	value,
	disabled,
}) => {
	const theme = useTheme<ApexTheme>();
	const handleOnClick = () => {
		if (!disabled) {
			onCardClick?.(name, value);
		}
	};

	return (
		<Box
			display="flex"
			justifyContent="center"
			alignItems="center"
			height={hieght ? hieght : '50px'}
			width={'245px'}
			opacity={disabled ? '0.5' : 1}
			pointerEvents={disabled ? 'none' : 'auto'}
			textDecorationColor={theme.colors.primary.default}
			border={
				selected
					? `2px solid ${theme.colors.primary.default}`
					: `1px solid ${theme.colors.monochrome.input}`
			}
			borderRadius={'8px'}
			onClick={handleOnClick}
			hoverProps={{
				cursor: 'pointer'
			}}
		>
			{children}
		</Box>
	);
};
