import { ApexTheme } from '@base/theme';
import { CSSProperties } from 'react';
import { createUseStyles } from 'react-jss';

export type StyleProps = CSSProperties;

export const useStyles: any = createUseStyles<any, StyleProps, ApexTheme>(
	(theme: ApexTheme) => ({
		container: {
			height: '100%',
		},
		tableCell: {
			width: '100%',
		},
		titleLabel: {
			fontSize: theme.fontSizes.desktop.bLarge,
			fontWeight: theme.fontWeights.bold,
			lineHeight: theme.lineHeights.desktop.bHuge,
			letterSpacing: theme.letterSpacings.desktop.bMedium,
			marginTop: '20px',
			marginLeft: '20px',
		},
		windowContainerStyles: {
			width: '500px !important',
			height: 'calc(100vh - 73px) !important',
			marginTop: `73px`,
		},
		popupHeight: {
			height: 'calc(100vh - 72px) !important',
			marginTop: '72px',
		},
		expanded: {
			width: '710px !important',
		},
		iconContainerStyle: {
			borderRadius: '50%',
			padding: '7px',
			width: '28px',
			height: '28px',
			justifyContent: 'center',
			alignItems: 'center',
			cursor: 'pointer',
		},
		iconContainer: {
			left: '50%',
			position: 'absolute',
			top: '50%',
			transform: 'translate(-50%, -50%)',
		},
	})
);
