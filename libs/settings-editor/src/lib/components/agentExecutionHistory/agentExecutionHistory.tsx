/* eslint-disable-next-line */
import {
	useEffect,
	useState,
	CSSProperties,
	FC,
	useCallback,
	useLayoutEffect,
} from 'react';
import DataTable from '@composite/datatable';
import { ApexTheme } from '@base/theme';
import { useTheme } from 'react-jss';
import Icon, { IconCodes } from '@atomic/icon';
import Box from '@atomic/box';
import Text from '@atomic/text';
import { STATUS } from './constants';
import { useTableViewModalStyles } from './styles';
import { DocumentExecutionHistoryProps } from './types';
import { debounce } from 'lodash';
import {
	Loader,
	LoaderType,
	PopUpMenu,
	commonUtils,
} from '@composite/doceditorviewer';
import { getColumns, getData, getExecutionDetails } from './helper';
import AgentService, { AgentServiceClass } from './agentApis';
import { AgentExecutionDetails } from './Components/ExecutionDetails/agentExecutionHistoryDetails';

export interface AgentExecutionHistoryProps {
	id: string;
	applicationId: string;
	tenantId: string;
}

const AgentExecutionHistory = (props: any) => {
	const { applicationId, tenantId } = props;
	const [rowData, setRowData] = useState<Record<string, string>[]>();
	const [showExecutionDetailsModal, setShowExecutionDetailsModal] =
		useState(false);
	const [currentRowExecutionDetails, setCurrentRowExecutionDetails] = useState<
		Record<string, string>[]
	>([]);

	const theme: ApexTheme = useTheme();

	const classes = useTableViewModalStyles(theme);

	const columnData = getColumns(theme, classes);
	const dataTableStyle = useCallback(
		(theme: ApexTheme): CSSProperties => ({
			margin: 'auto',
			borderRadius: '8px',
			background: theme.colors.monochrome.white,
			textDecorationColor: theme.colors.monochrome.label,
			color: theme.colors.monochrome.label,
			padding: '0px !important',
			minHeight: '250px',
		}),
		[]
	);

	useEffect(() => {
		AgentService.init(applicationId, '', tenantId);
		loadData();
	}, []);

	const loadData = async (status = '', pageNumber = 0, pageSize = 20) => {
		const { data, totalCount } = await getData();
		setRowData(data);
	};

	const handleExecutionDetailsModalClose = () => {
		setShowExecutionDetailsModal(false);
	};

	const handleRowClick = async (value: any) => {
		const executionDetails = await getExecutionDetails(value?.trace_id);
		setCurrentRowExecutionDetails(executionDetails);
		setShowExecutionDetailsModal(true);
	};

	const data = [
		{
			"trace_id": "06c79a7610594efcb62fbe3cbd3ccc1d",
			"workflow_name": "summarizer_agent",
			"started_at": "2025-07-08T10:32:36.547611",
			"ended_at": "2025-07-08T10:32:36.547611",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "6c566dc4a6de4e7495973c6bec11c021",
			"workflow_name": "summarizer_agent",
			"started_at": "2025-07-08T10:19:21.301086",
			"ended_at": "2025-07-08T10:19:21.301086",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "0044d56867704ca09af587d2b3cd0b4a",
			"workflow_name": "summarizer_agent",
			"started_at": "2025-07-08T10:17:43.673691",
			"ended_at": "2025-07-08T10:17:43.673691",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "0443ae8fe20d4229966a38e3e3828747",
			"workflow_name": "summarizer_agent",
			"started_at": "2025-07-08T10:13:49.117694",
			"ended_at": "2025-07-08T10:13:49.117694",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "a378e149cd0f404b91cc37d5075463c5",
			"workflow_name": "summarizer_agent",
			"started_at": "2025-07-08T10:08:52.773517",
			"ended_at": "2025-07-08T10:08:52.773517",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "fef8c33482cb46958acf89f1c52eb90a",
			"workflow_name": "summarizer_agent",
			"started_at": "2025-07-08T10:00:36.384188",
			"ended_at": "2025-07-08T10:00:36.384188",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "ff942ecb73b2412eafbb74b2c6222ef5",
			"workflow_name": "summarizer_agent",
			"started_at": "2025-07-08T10:00:09.780870",
			"ended_at": "2025-07-08T10:00:09.780870",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "bbb996cbcce0493f96da88cb587d7174",
			"workflow_name": "summarizer_agent",
			"started_at": "2025-07-08T07:07:01.305384",
			"ended_at": "2025-07-08T07:07:01.305384",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "700154894a4f442894c3f8cb7b780209",
			"workflow_name": "search_agent",
			"started_at": "2025-07-08T04:10:14.091331",
			"ended_at": "2025-07-08T04:10:14.091331",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "d3d8e11991024c63943b4319502c8dbf",
			"workflow_name": "sentiment_categorisation_agent",
			"started_at": "2025-07-07T14:52:02.833589",
			"ended_at": "2025-07-07T14:52:02.833589",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "0ef62d07011b412a8199159d9470d52b",
			"workflow_name": "search_agent",
			"started_at": "2025-07-07T14:43:30.013175",
			"ended_at": "2025-07-07T14:43:30.013175",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "9728cbc5330d4fa3ae8b0656d85e8486",
			"workflow_name": "sentiment_categorisation_agent",
			"started_at": "2025-07-07T12:16:58.986773",
			"ended_at": "2025-07-07T12:16:58.986773",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "9728cbc5330d4fa3ae8b0656d85e8486",
			"workflow_name": "Sentiment categorisation agent",
			"started_at": "2025-07-07T11:44:14.239370",
			"ended_at": "2025-07-07T11:44:14.239370",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "aea7363a5ac14b819ecd32b560c93b5b",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T11:40:29.124531",
			"ended_at": "2025-07-07T11:40:29.124531",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "ea8dd674-0496-4842-8a67-c44389ece9c8",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T11:37:30.893287",
			"ended_at": "2025-07-07T11:37:30.893287",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "8a15e7ac-d0e8-490b-8ce2-c27dc90b25c7",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T11:36:58.588342",
			"ended_at": "2025-07-07T11:36:58.588342",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "8cf9a085-2100-4ac9-88de-406097d54de9",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T11:36:39.319910",
			"ended_at": "2025-07-07T11:36:39.319910",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "f7227c30-06af-4394-a20b-6ed9b19ad69a",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T11:36:22.708582",
			"ended_at": "2025-07-07T11:36:22.708582",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "cc8562fd-76c0-4610-9735-2ec127333836",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-07T11:23:24.579772",
			"ended_at": "2025-07-07T11:23:24.579772",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "361956d0-1058-42d9-b9a3-defdd28c9f0a",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-07T11:22:32.473064",
			"ended_at": "2025-07-07T11:22:32.473064",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "015c8da9-7c2e-4c02-9f1f-bbefab16bc1a",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-07T11:02:39.876104",
			"ended_at": "2025-07-07T11:02:39.876104",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "session_1",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-07T09:44:44.074036",
			"ended_at": "2025-07-07T09:44:44.074036",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "fa476db1-7a52-4f0d-9500-4fba1dacb4d4",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T08:52:08.134753",
			"ended_at": "2025-07-07T08:52:08.134753",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "1cc48fff-0afb-4d2b-a876-4e7a10b7c602",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T08:51:57.337031",
			"ended_at": "2025-07-07T08:51:57.337031",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "059295ea-2128-43ef-83f2-2723a0aa8737",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T08:51:39.072611",
			"ended_at": "2025-07-07T08:51:39.072611",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "a2f5a69e-dc23-4af0-bac2-a0f72e05ea36",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T07:49:17.717374",
			"ended_at": "2025-07-07T07:49:17.717374",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "f7b0a62d-ed90-4bb1-9ec4-79f1a13692c6",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T07:48:56.527556",
			"ended_at": "2025-07-07T07:48:56.527556",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "726f570f-bfdc-40ba-8b15-b6465cc0d0ce",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T07:48:37.591765",
			"ended_at": "2025-07-07T07:48:37.591765",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "6a9ba8db-fc29-4643-a17c-d73c4aa6a4dc",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T07:48:15.178116",
			"ended_at": "2025-07-07T07:48:15.178116",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "33467061-3fb5-455b-9c96-17f5c9df54b7",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-07T07:47:42.430756",
			"ended_at": "2025-07-07T07:47:42.430756",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "7ff5fe2d-f164-41b2-a5cb-a1a9fb2d3a2b",
			"workflow_name": "d1j9rka6c6dc9fmle2dg",
			"started_at": "2025-07-07T07:22:44.189853",
			"ended_at": "2025-07-07T07:22:44.189853",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "9777c4b1-939f-4c99-b7ab-637ab1c864d2",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-05T17:54:11.319512",
			"ended_at": "2025-07-05T17:54:11.319512",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "b5d7f178-40a8-4bdf-9cda-76b375d74cbc",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-05T17:46:00.829166",
			"ended_at": "2025-07-05T17:46:00.829166",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "88d212e0-143e-413f-ab70-2d4b8b06df3c",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-05T17:00:34.348159",
			"ended_at": "2025-07-05T17:00:34.348159",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "c1f7d681-8fde-4fc9-a084-9125c59a389e",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-05T17:00:16.654218",
			"ended_at": "2025-07-05T17:00:16.654218",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "1914b8c7-c476-4def-8d6f-06951d4354d5",
			"workflow_name": "d1kh2r69j1noa6be7ea0",
			"started_at": "2025-07-05T16:59:31.664160",
			"ended_at": "2025-07-05T16:59:31.664160",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "fa424653-2b02-48b8-aa40-eb04a026f471",
			"workflow_name": "d1kds269j1noa6be7e90",
			"started_at": "2025-07-05T14:02:33.539591",
			"ended_at": "2025-07-05T14:02:33.539591",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "4426e88e-5272-442e-aed0-ece26a6a075f",
			"workflow_name": "d1kds269j1noa6be7e90",
			"started_at": "2025-07-05T14:01:42.045910",
			"ended_at": "2025-07-05T14:01:42.045910",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "9bb657fc-69d7-4ae4-9f12-f2ee26330d5f",
			"workflow_name": "d1kds269j1noa6be7e90",
			"started_at": "2025-07-05T14:01:26.848216",
			"ended_at": "2025-07-05T14:01:26.848216",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "a68d7590-1262-4552-82ee-1bb2721bfc93",
			"workflow_name": "d1kds269j1noa6be7e90",
			"started_at": "2025-07-05T14:00:43.661901",
			"ended_at": "2025-07-05T14:00:43.661901",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "0082ff64-5e37-4fd6-92b7-3fc674475eb4",
			"workflow_name": "d1kds269j1noa6be7e90",
			"started_at": "2025-07-05T13:59:56.501863",
			"ended_at": "2025-07-05T13:59:56.501863",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "19033aab-5ebc-4f2b-b897-c40f7cf7a97e",
			"workflow_name": "d1kds269j1noa6be7e90",
			"started_at": "2025-07-05T13:59:27.077099",
			"ended_at": "2025-07-05T13:59:27.077099",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "37d5bf10-1d20-4ac1-841a-e6bd56ee7280",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-05T12:00:41.751892",
			"ended_at": "2025-07-05T12:00:41.751892",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "618a8bda-4e9d-4d94-b635-1f7a57982b35",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-05T11:49:43.080782",
			"ended_at": "2025-07-05T11:49:43.080782",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "3e753073-26ca-4d33-a85a-ac3d18b3a12c",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-05T08:12:28.495687",
			"ended_at": "2025-07-05T08:12:28.495687",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "1a14258b-c7d8-486e-b4a5-2dc0919f5506",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-05T08:11:39.128705",
			"ended_at": "2025-07-05T08:11:39.128705",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "cd431c00-a18b-4749-9f69-c954904fadf4",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-05T08:10:17.689894",
			"ended_at": "2025-07-05T08:10:17.689894",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "e1a7633d-a9a3-4e5a-80f2-f8c9952b2c65",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-05T06:26:09.756049",
			"ended_at": "2025-07-05T06:26:09.756049",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "2dbbbdf4-466d-4cac-a8a7-db06ef0f3067",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-05T06:24:47.823409",
			"ended_at": "2025-07-05T06:24:47.823409",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "06b379b6-1063-40e7-b4a3-b8e139322f2f",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-05T06:23:55.535934",
			"ended_at": "2025-07-05T06:23:55.535934",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "8f03a947-53aa-4ec9-b254-9a7bc14051ab",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-05T06:11:46.357369",
			"ended_at": "2025-07-05T06:11:46.357369",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "63325767-d82d-4e3e-8a24-5fee005906f9",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T09:43:53.138452",
			"ended_at": "2025-07-04T09:43:53.138452",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "72787335-c32c-4115-b955-1c46c76d0837",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T09:07:12.654105",
			"ended_at": "2025-07-04T09:07:12.654105",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "226c28c0-b9b7-44aa-a818-9788551224e3",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T08:30:08.941376",
			"ended_at": "2025-07-04T08:30:08.941376",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "e55a7861-0488-49fb-be29-a4ba8ce5d87d",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T08:28:55.665116",
			"ended_at": "2025-07-04T08:28:55.665116",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "9e5c16e5-9b0f-4a0a-9df2-a35c33bee8f7",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T08:24:44.518039",
			"ended_at": "2025-07-04T08:24:44.518039",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "66166fba-2d84-43a2-bec2-3bb577352bbb",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T08:23:57.778098",
			"ended_at": "2025-07-04T08:23:57.778098",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "7e143377-47ff-4c71-97af-33af3cbc9de4",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T07:51:17.730429",
			"ended_at": "2025-07-04T07:51:17.730429",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "d9246502-12be-45bc-a052-9b4b31530216",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T07:48:34.314772",
			"ended_at": "2025-07-04T07:48:34.314772",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "95a7a429-1ce4-469b-b1cc-0e4c7e96bbd4",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T07:23:45.491896",
			"ended_at": "2025-07-04T07:23:45.491896",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "5fd9976c-aa9e-4d63-906c-e056ef9fd51c",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T05:45:06.024978",
			"ended_at": "2025-07-04T05:45:06.024978",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "cff209f3-8f22-45db-80d2-384487ed819c",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T05:15:29.570408",
			"ended_at": "2025-07-04T05:15:29.570408",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "59acfde7-02b5-4caa-ad39-41b6a20dacb2",
			"workflow_name": "d1hssko2e292tma7ni10",
			"started_at": "2025-07-04T03:52:43.060897",
			"ended_at": "2025-07-04T03:52:43.060897",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "564ad50c-dc47-44da-b53c-894dae83d461",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T03:51:27.940812",
			"ended_at": "2025-07-04T03:51:27.940812",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "372c567f-70c4-4bab-8025-783bec04fce3",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T03:40:11.533324",
			"ended_at": "2025-07-04T03:40:11.533324",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "837317d7-104a-4b82-9855-8e3b2cfdf5fc",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T02:52:37.960752",
			"ended_at": "2025-07-04T02:52:37.960752",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "84d37738-d85e-402a-ad65-56f53e4827d5",
			"workflow_name": "d1jjav73agf0h99fqvig",
			"started_at": "2025-07-04T02:42:09.968109",
			"ended_at": "2025-07-04T02:42:09.968109",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "fad8f231-915b-4238-958b-2519c32a19c3",
			"workflow_name": "d1jjav73agf0h99fqvig",
			"started_at": "2025-07-04T02:41:44.539032",
			"ended_at": "2025-07-04T02:41:44.539032",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "cdf9c606-bc67-40b2-979c-8aa4a5b61f89",
			"workflow_name": "d1hssko2e292tma7ni10",
			"started_at": "2025-07-04T02:24:03.077926",
			"ended_at": "2025-07-04T02:24:03.077926",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "22b39fe9-26f1-496b-abc9-71b82d5ec49b",
			"workflow_name": "d1hssko2e292tma7ni10",
			"started_at": "2025-07-04T02:23:04.738996",
			"ended_at": "2025-07-04T02:23:04.738996",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "20a47e13-f94e-4162-b838-593defa54709",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-04T01:59:04.408687",
			"ended_at": "2025-07-04T01:59:04.408687",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "213ceea1-591c-4d4a-bd50-42c602864018",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T18:08:13.313298",
			"ended_at": "2025-07-03T18:08:13.313298",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "c4fe60d3-42de-42d1-9f17-d39555c10da1",
			"workflow_name": "d18jpocvvie625nhkvlg",
			"started_at": "2025-07-03T15:53:15.558475",
			"ended_at": "2025-07-03T15:53:15.558475",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "e35e3ae5-245e-4559-a006-7dbe2c2806a7",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:44:26.334886",
			"ended_at": "2025-07-03T15:44:26.334886",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "f21a0cdb-067e-4283-a98d-11b5ef56f80d",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:43:13.575147",
			"ended_at": "2025-07-03T15:43:13.575147",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "5169af44-de82-4728-815b-9940fe63e7df",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:41:36.805891",
			"ended_at": "2025-07-03T15:41:36.805891",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "01d99b5d-07ae-4764-9b14-96143e88e8b9",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:40:08.848866",
			"ended_at": "2025-07-03T15:40:08.848866",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "5741db60-3ea9-499f-86f7-61c4554124ac",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:37:45.266330",
			"ended_at": "2025-07-03T15:37:45.266330",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "ed323e1a-57bf-4035-9c90-b10fdbe7dbe9",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:37:22.915909",
			"ended_at": "2025-07-03T15:37:22.915909",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "1828bf95-d9b5-4c4c-ac76-49534dcc5b24",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:34:13.257607",
			"ended_at": "2025-07-03T15:34:13.257607",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "4f6fe58a-9629-486f-bf0e-768b4ad31a77",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:33:23.888144",
			"ended_at": "2025-07-03T15:33:23.888144",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "5200e7aa-8599-4bcf-9413-38be8c744b98",
			"workflow_name": "d164fgigqtbb7fesn8j0",
			"started_at": "2025-07-03T15:32:06.985687",
			"ended_at": "2025-07-03T15:32:06.985687",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "840e0560-d206-4c2a-9fea-55200ee84595",
			"workflow_name": "d11tjuddjtbdqcpbuto0",
			"started_at": "2025-07-03T15:27:33.908253",
			"ended_at": "2025-07-03T15:27:33.908253",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "d1b349a6-525b-4ff9-9557-4f0e9b98f75c",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:24:22.376575",
			"ended_at": "2025-07-03T15:24:22.376575",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "2d6118e7-6c64-46eb-82b8-3d0b0ed952ce",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:23:24.809720",
			"ended_at": "2025-07-03T15:23:24.809720",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "ddbeca6d-f29e-441a-8b04-ae58119db6f5",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:22:45.103601",
			"ended_at": "2025-07-03T15:22:45.103601",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "3330fdc4-0fb9-48e0-868e-1ba4e562cd54",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:17:10.459695",
			"ended_at": "2025-07-03T15:17:10.459695",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "43849ae7-cfb1-40c3-ae9b-23d94e25babf",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:12:41.499311",
			"ended_at": "2025-07-03T15:12:41.499311",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "183f9807-ad82-4697-a67d-c3d9d1a256bc",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:11:27.669086",
			"ended_at": "2025-07-03T15:11:27.669086",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "1c30bb4f-a936-4869-bf66-c59cc0a68c51",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:07:36.304030",
			"ended_at": "2025-07-03T15:07:36.304030",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "ca2dfa25-d93a-4520-a734-3e54bc3fa45a",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:07:13.222334",
			"ended_at": "2025-07-03T15:07:13.222334",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "62b96346-298f-43c1-b97b-caa304950aa0",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T15:02:35.359568",
			"ended_at": "2025-07-03T15:02:35.359568",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "204b2816-230d-491c-bff4-632e8e6a57ed",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T14:59:44.508020",
			"ended_at": "2025-07-03T14:59:44.508020",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "aaeb3b53-ff66-4d81-a046-9c988faa2aca",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T14:58:23.821567",
			"ended_at": "2025-07-03T14:58:23.821567",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "796ddb3d-520e-4821-81a4-2377b59b9d5c",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T14:56:51.736740",
			"ended_at": "2025-07-03T14:56:51.736740",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "d2b9de5f-6240-43a2-91fe-e3e976dd6e06",
			"workflow_name": "d18jpocvvie625nhkvlg",
			"started_at": "2025-07-03T14:44:04.426613",
			"ended_at": "2025-07-03T14:44:04.426613",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "6177e617-d4f9-4c3b-8d6c-603c62555f9a",
			"workflow_name": "d18jpocvvie625nhkvlg",
			"started_at": "2025-07-03T14:43:15.182910",
			"ended_at": "2025-07-03T14:43:15.182910",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "98b90837-4b24-4aef-b4e7-c70e2c37404b",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T14:32:29.570935",
			"ended_at": "2025-07-03T14:32:29.570935",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "1a6c45e5-dffa-4d66-9f90-42c07c1918e8",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T14:30:23.741684",
			"ended_at": "2025-07-03T14:30:23.741684",
			"has_error": false,
			"status": "Completed"
		},
		{
			"trace_id": "eff481c1-8eb4-4cbd-8090-bf04aa649842",
			"workflow_name": "agent_creation_agent",
			"started_at": "2025-07-03T13:57:22.409599",
			"ended_at": "2025-07-03T13:57:22.409599",
			"has_error": false,
			"status": "Completed"
		}
	]

	return (
		<Box
			width="100%"
			display="flex"
			flexDirection="column"
			gap="20px"
		>
				<DataTable
					id="agentExecutionHistory"
					height="fit-content"
					maxHeight="calc(100% - 50px)"
					width="100%"
					margin="unset"
					decoratorStyles={{ display: 'contents' }}
					noRecordsImageProps={{ display: 'none' }}
					cellWrapperStyleClass={classes.tableCell}
					rowData={rowData}
					onRowClick={handleRowClick}
					enableFilter={false}
					columnData={columnData}
					headerRowHeight={56}
					enableGlobalSearch={false}
					enableGrouping={false}
					enableColumnSelection={false}
					isExportEnabled={false}
					showMoreOption={false}
					rowHeight={48}
					showFooter={false}
					allowPagination={false}
					style={dataTableStyle(theme)}
					showColumnSelect={false}
					disableUISearch
					placeholder="Search by name"
					totalRecordsCount={0}
					showColumnFilter={false}
					isSortingEnabled
				/>
			{showExecutionDetailsModal && currentRowExecutionDetails && (
				<AgentExecutionDetails
					onModalClose={handleExecutionDetailsModalClose}
					currentRowExecutionDetails={currentRowExecutionDetails}
				/>
			)}
		</Box>
	);
};

export default AgentExecutionHistory;
