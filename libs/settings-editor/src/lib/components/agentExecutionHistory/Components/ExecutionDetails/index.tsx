import Icon, { Animations, IconCodes } from '@atomic/icon';
import Modal, { DockType, WindowSize } from '@composite/modal';
import React, { useEffect, useState } from 'react';
import { useTheme } from 'react-jss';
import { TextSizes } from '@atomic/text';
import { ApexTheme } from '@base/theme';
import Box from '@atomic/box';
import Tab, { TabsTypes, TabVariant } from '@atomic/segmenttabs';
import TabNavigation, { Content } from '@atomic/tabnavigation';
import Text from '@atomic/text';
import { isNull } from 'lodash';
import {
	getIconProps,
	getJsonValue,
	getRunTimeFromDuration,
	getStatusText,
	formatDate,
} from '../../helpers';
import { DEBUG_VAR_NAME, MSGS } from '../../constants';
import Info, { InfoTypes } from '@atomic/info';
import RunLog from '../NodeDebugDetails/RunLog';
import Tooltip, { TooltipPosition, TooltipType } from '@atomic/tooltip';
import { useStyles } from '../../styles';
import Button, { ButtonRadius, ButtonTypes } from '@atomic/button';
import Service from '../../base';
import _set from 'lodash/set';
import JSONTree from '../NodeDebugDetails/JSONTree';
export interface PopupRendererProps {
	handleClose?: (value: boolean) => void;
	showExecutionDetailsPopup?: boolean;
	executionDetails: Record<string, any>;
	traceElement: any;
	setTraceElement: any;
	runLogData: any;
	workflowId?: string;
	showNodeRunDetails: (log: any) => void;
	setNextElementForTrace: () => void;
	setPrevElementForTrace: () => void;
	setShowRunLogTrace: (data?: any) => void;
	setSelectedNodeDetails: (data?: any) => void;
	showNodeDebugDetailsPopup: boolean;
	expanded: boolean;
	setExpanded: (data?: any) => void;
	setStartNodeOutput: (data?: any) => void;
	setStartNodeInput: (data?: any) => void;
	showExecutionHistory?: any;
	setRunLogData?: any;
	hideButtons?: boolean;
	workflow: any;
	showExpandHandle?: boolean;
	fromCanvas?: boolean;
	fetchLogStatements?: (
		workflowId: string,
		startedAt: number,
		closedTime: number
	) => void;
}

const tabStyleProps = (theme: ApexTheme) => ({
	fontWeight: theme.fontWeights.XBold,
	color: theme.colors.monochrome.label,
	padding: '8px 4px',
});

const ExecutionDetails: React.FC<PopupRendererProps> = (props): JSX.Element => {
	const {
		handleClose,
		showExecutionDetailsPopup,
		executionDetails,
		traceElement,
		setTraceElement,
		runLogData,
		workflowId = '',
		showNodeRunDetails,
		setNextElementForTrace,
		setPrevElementForTrace,
		setShowRunLogTrace,
		setSelectedNodeDetails,
		expanded,
		setExpanded,
		setStartNodeInput,
		setStartNodeOutput,
		setRunLogData,
		hideButtons,
		workflow,
		showExpandHandle = true,
		fromCanvas,
		fetchLogStatements,
	} = props;
	const theme: ApexTheme = useTheme();
	const {
		startedAt,
		closedTime,
		duration,
		output,
		input,
		error,
		logs = [],
	} = executionDetails;
	const logStatements = logs?.sort(
		(a: any, b: any) =>
			new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
	);
	const [activeTab, setActiveTab] = useState('Output');
	const classes = useStyles();
	const [outputData, setOutputData] = useState({});
	const [inputData, setInputData] = useState(input ? JSON.parse(input) : {});
	const buttonStyles = {
		width: '40px',
		height: '40px',
		padding: 0,
		minWidth: 'fit-content',
		backgroundColor: 'transparent',
	};
	const buttonBarStyle = {
		padding: '8px 10px',
	};

	const onModalClose = () => {
		handleClose?.(false);
	};

	const handleTabSelect = (tab: string) => {
		setActiveTab(tab);
		if (tab === 'Run logs') {
			setShowRunLogTrace({
				traceElement: runLogData?.events[0]?.stepName,
				index: 0,
				showPopup: true,
			});
			setSelectedNodeDetails(runLogData.events[0]);
		} else {
			setShowRunLogTrace({
				index: -1,
				traceElement: undefined,
				showPopup: true,
			});
			setSelectedNodeDetails(undefined);
		}
	};

	const getOutputDataForDisplay = (output: any) => {
		if (typeof output !== 'object') return Promise.resolve(output);
		const outputUpdated = { ...output };
		try {
			const attr = workflow.attributes.findLast(
				(attr: any) => attr.name === runLogData?.outputVariable
			);
			if (attr.association.attribute.targetId)
				return Service.getAttributeLabels(
					workflow.service.uuid,
					attr.association.attribute.targetId,
					output
				).then(
					(labels) => {
						return labels.data || outputUpdated;
					},
					() => outputUpdated
				);
			else return Promise.resolve(outputUpdated);
		} catch (e) {
			return Promise.resolve(outputUpdated);
		}
	};

	const transformInputDataForDisplay = (input: any) => {
		const inputUpdated = input && JSON.parse(input);
		let promises: Promise<any>[] = [];
		try {
			const keys = Object.keys(inputUpdated);
			promises = keys.map(async (key) => {
				const attribute = workflow.attributes.findLast(
					(attr: any) => attr.name === key
				);

				if (attribute?.association?.attribute?.targetId) {
					return Service.getAttributeLabels(
						workflow.service.uuid,
						attribute.association.attribute.targetId,
						inputUpdated[key]
					).then(
						(labels) => {
							return {
								[key]: labels.data || inputUpdated[key],
							};
						},
						() => inputUpdated[key]
					);
				}
			});
		} catch (e) {
			return promises;
		}

		return promises;
	};

	useEffect(() => {
		(async () => {
			const newData = input ? JSON.parse(input) : {};
			getOutputDataForDisplay(output).then((data) => {
				if (runLogData?.outputVariable) {
					setOutputData({ [runLogData?.outputVariable]: data });
					setStartNodeOutput({ [runLogData?.outputVariable]: data });
				} else {
					setOutputData(data);
					setStartNodeOutput(data);
				}
			});
			const values = await Promise.all(transformInputDataForDisplay(input));
			values.forEach((item) => {
				if (item)
					for (const [key, value] of Object.entries(item)) {
						_set(newData, key, value);
					}
			});
			if (newData[DEBUG_VAR_NAME]) delete newData[DEBUG_VAR_NAME];
			setInputData(newData);
			setStartNodeInput(newData);
		})();
	}, [output, input]);

	const getHeaderComponent = () => {
		return (
			<Box justifyContent="space-between">
				<Text
					fontSize={theme.fontSizes.desktop.bLarge}
					fontWeight={theme.fontWeights.semiBold}
					lineHeight={theme.lineHeights.desktop.bHuge}
					color={theme.colors.monochrome.body}
				>
					Execution details
				</Text>
				<Box
					gap="4px"
					alignItems="center"
					position="absolute"
					right={'80px'}
					top="23px"
				>
					<Icon
						{...getIconProps(runLogData.status, theme)}
						fontSize={theme.fontSizes.desktop.hXSmall}
					/>
					<Text
						color={theme.colors.monochrome.body}
						fontSize={theme.fontSizes.desktop.bSmall}
					>
						{getStatusText(runLogData.status)}
					</Text>
				</Box>
			</Box>
		);
	};
	const Loader = () => (
		<div className={classes.iconContainer}>
			<div>
				<Icon
					icon={IconCodes.icon_Tb_loader}
					color={theme.colors.green.default}
					fontSize={theme.fontSizes.desktop.hSmall}
					animation={Animations.Spin}
				/>
			</div>
		</div>
	);
	return (
		<>
			<Modal
				showModal={true}
				isButtonBarEnabled={false}
				isTitleBarEnabled={true}
				windowSize={WindowSize.Medium}
				headerProps={{
					title: 'Execution details',
					titleStyles: {
						size: TextSizes.Medium,
					},
					tooltipProps: { isOpen: false },
				}}
				isDefaultModal
				headerComponentRender={getHeaderComponent}
				headerButtonArray={[
					{
						buttonStyles,
						iconProps: {
							icon: IconCodes.icon_Bd_Close_X,
							height: '18px',
							fontSize: theme.fontSizes.desktop.bMedium,
						},
					},
				]}
				closeClickHandler={onModalClose}
				headerButtonBarProps={{
					buttonBarProps: buttonBarStyle,
				}}
				headerLeftButtonArray={[
					{
						iconProps: {
							icon: IconCodes.icon_Bd_Back_Arrow,
							onClick: onModalClose,
							fontSize: theme.fontSizes.desktop.bMedium,
							color: theme.colors.monochrome.label,
						},
					},
				]}
				dockType={DockType.Right}
				modalStyle="Modern"
				windowContainerProps={{ zIndex: 12 }}
				className={`${classes.windowContainerStyles} ${classes.popup} ${
					showExecutionDetailsPopup ? classes.show : ''
				} ${expanded ? classes.expanded : ''} ${
					fromCanvas ? classes.popupHeight : ''
				}`}
			>
				<Box flexDirection="column" width="100%">
					<Box
						justifyContent={'space-between'}
						width="100%"
						backgroundColor={theme.colors.monochrome.white}
						padding="10px"
					>
						<Box padding="10px">
							<Info
								label="Run date and time"
								value={new Date(startedAt * 1000).toString().substring(4, 24)}
								type={InfoTypes.TEXT}
								name={''}
							></Info>
						</Box>
						<Box padding="10px">
							<Info
								label="Total time"
								value={getRunTimeFromDuration(duration)}
								type={InfoTypes.TEXT}
								name={''}
							></Info>
						</Box>
					</Box>
					<TabNavigation
						tabsAndContentContainerProps={{ width: '100%' }}
						type={TabsTypes.ValueOnly}
						defaultSelectedTab={error ? 2 : 0}
						variant={TabVariant.Default}
						isOverflow={false}
						tabsContainerStyle={{
							padding: '0 5px',
							gap: '0px',
						}}
						onTabSelect={handleTabSelect}
					>
						<Tab
							title="Output"
							key="Output"
							overflowLabel="Output"
							tabStyleProps={tabStyleProps(theme)}
							tabTextStyle={{ fontWeight: theme.fontWeights.semiBold }}
						>
							<Content contentStyle={{ padding: '20px' }}>
								<Box width={'100'}>
									{output === 'Done' && (
										<Text color={theme.colors.monochrome.label}>
											{MSGS.DEFAULT_OUTPUT_MSG}
										</Text>
									)}
									{!isNull(output) &&
										output !== 'Done' &&
										(typeof output !== 'object' ? (
											<JSONTree data={outputData} />
										) : Array.isArray(output) && output.length === 0 ? (
											<Text color={theme.colors.monochrome.label}>
												{MSGS.EMPTY_OUTPUT_MSG}
											</Text>
										) : (
											<JSONTree data={outputData} />
										))}
								</Box>
							</Content>
						</Tab>
						<Tab
							title="Input"
							key="Input"
							overflowLabel="Input"
							tabStyleProps={tabStyleProps(theme)}
							tabTextStyle={{ fontWeight: theme.fontWeights.semiBold }}
						>
							<Content contentStyle={{ padding: '20px' }}>
								<Box>
									<JSONTree data={inputData ? inputData : {}} />
								</Box>
							</Content>
						</Tab>

						{error ? (
							<Tab
								title="Error log"
								key="Error Information"
								overflowLabel="Error Information"
								tabStyleProps={tabStyleProps(theme)}
								tabTextStyle={{ fontWeight: theme.fontWeights.semiBold }}
							>
								<Content contentStyle={{ padding: '5px' }}>
									<Box flexDirection="column" gap="10px">
										{/* <Text
                      color={theme.colors.monochrome.label}
                      size={TextSizes.Small}
                    >
                      {'Message'}
                    </Text> */}
										{typeof getJsonValue(error) !== 'object' ? (
											<Box padding="20px">
												<Text
													color={theme.colors.secondary[600]}
													size={TextSizes.Small}
												>
													{error}
												</Text>
											</Box>
										) : (
											<Box color={theme.colors.secondary[600]}>
												<JSONTree data={getJsonValue(error)} isError />
											</Box>
										)}
									</Box>
								</Content>
							</Tab>
						) : (
							<></>
						)}
						<Tab
							title="Run logs"
							key="RunLogs"
							overflowLabel="Run logs"
							tabStyleProps={tabStyleProps(theme)}
							tabTextStyle={{ fontWeight: theme.fontWeights.semiBold }}
						>
							<Content
								contentStyle={{ flexDirection: 'column', width: '100%' }}
							>
								<RunLog
									traceElement={traceElement}
									setTraceElement={setTraceElement}
									runLogData={runLogData}
									workflowId={workflowId}
									showNodeRunDetails={showNodeRunDetails}
									setRunLogData={setRunLogData}
								/>
							</Content>
						</Tab>
						<Tab
							title="Log statements"
							key="Logs"
							overflowLabel="Logs"
							tabStyleProps={tabStyleProps(theme)}
							tabTextStyle={{ fontWeight: theme.fontWeights.semiBold }}
							onClick={() =>
								logStatements?.length == 0 &&
								fetchLogStatements?.(workflowId, startedAt, closedTime)
							}
						>
							<Content
								contentStyle={{
									flexDirection: 'column',
									width: '100%',
									padding: '0 16px',
									gap: '20px',
									maxHeight: 'calc(100vh - 18rem)',
									overflowY: 'auto',
								}}
							>
								{logStatements?.length == 0 && <Loader />}
								{logStatements?.length > 0 &&
									logs.map((log: any) => (
										<Box gap={'10px'} key={log.timestamp}>
											<Box whiteSpace="nowrap">
												<Text
													color={theme.colors.monochrome.body}
													fontWeight={theme.fontWeights.semiBold}
												>
													{log.timestamp && (
														<Text color={theme.colors.monochrome.label}>
															{formatDate(log.timestamp)}
														</Text>
													)}
												</Text>
											</Box>
											<Box></Box>

											<Text color={theme.colors.monochrome.label}>
												{log.message}
											</Text>
										</Box>
									))}
							</Content>
						</Tab>
					</TabNavigation>
				</Box>
			</Modal>
			{showExpandHandle && (
				<Box
					position={fromCanvas ? 'absolute' : 'fixed'}
					top="50vh"
					right={expanded ? '690px' : '480px'}
					zIndex={'4'}
					boxShadow="rgba(0, 0, 0, 0.2) 0px 0px 8px"
					borderRadius="40px"
				>
					<Button
						buttonType={ButtonTypes.Subtle}
						onClick={() => setExpanded(!expanded)}
						radius={ButtonRadius.SemiRounded}
						buttonStyles={{
							backgroundColor: theme.colors.monochrome.offWhite,
							border: `1px solid ${theme.colors.monochrome.offWhite}`,
							width: '60px',
							minWidth: '60px',
							borderRadius: '45%',
						}}
						hoverStyles={{
							border: `1px solid ${theme.colors.monochrome.offWhite}`,
						}}
						activeStyles={{
							border: `1px solid ${theme.colors.monochrome.offWhite}`,
						}}
						iconProps={{
							icon: expanded
								? IconCodes.icon_Tb_chevron_right
								: IconCodes.icon_Tb_chevron_left,
							color: theme.colors.primary.default,
							fontSize: theme.fontSizes.desktop.hXSmall,
							height: '24px',
							iconStyle: { paddingRight: '12px' },
						}}
						title={''}
					/>
				</Box>
			)}
		</>
	);
};

export default React.memo(ExecutionDetails);
