import Box from '@atomic/box';
import Text from '@atomic/text';
import { ApexTheme, DeviceTypes } from '@base/theme';
import { useTheme } from 'react-jss';
import Icon, { Animations, IconCodes } from '@atomic/icon';
import { useCallback, useState } from 'react';
import { getRunTimeFromDuration } from './agentExecutionHistoryDetails';
import Info, { InfoTypes } from '@atomic/info';
import Scrollbar, { useScrollbarStyles } from '@atomic/scrollbar';
import { ActiveConvesationLogContent } from './constants';
import { SingleConversationLogs } from './singleConversationLog';

export interface ConversationLogsProps {
	data: Record<string, any>;
}

export interface ConversationContentProps {
	content: ActiveConvesationLogContent;
	logData: any;
}

export const ConversationLogs: React.FC<ConversationLogsProps> = ({
	data,
}): JSX.Element => {
	const { spans } = data;
	console.log('ConversationLogs', data);
	const theme: ApexTheme = useTheme();
	const scrollStyles = useScrollbarStyles();
	const [activeContent, setActiveContent] = useState<ConversationContentProps>({
		content: ActiveConvesationLogContent.ConversationLogs,
		logData: {},
	});

	const getIconProps = (hasError: Boolean) => {
		return !hasError
			? {
					icon: IconCodes.icon_Bd_Filled_Success,
					color: theme.colors.success.default,
			  }
			: {
					icon: IconCodes.icon_Bd_Filled_Close,
					color: theme.colors.red[500],
			  };
	};

	const handleConversationLogClick = (logData: any) => {
		console.log('logData', logData);

		setActiveContent({
			content: ActiveConvesationLogContent.SingleConversationLog,
			logData,
		});
	};

	const handleBackButtonClick = useCallback(() => {
		setActiveContent({
			content: ActiveConvesationLogContent.ConversationLogs,
			logData: {},
		});
	}, []);

	const renderContent = () => {
		if (
			activeContent.content === ActiveConvesationLogContent.ConversationLogs
		) {
			return (
				<Scrollbar
					maxHeight="calc(100% - 100px)"
					scrolbarContainerProps={{
						position: 'absolute',
						top: '80px',
						width: '100%',
					}}
				>
					{spans.map((spanItem: any, index: number) => {
						return (
							<Box
								flexDirection="column"
								padding={'15px'}
								onClick={() => {
									handleConversationLogClick(spanItem);
								}}
								width={'100%'}
								gap={'10px'}
								borderBottom={`1px solid ${theme.colors.monochrome.input}`}
								hoverProps={{
									cursor: 'pointer',
									boxShadow: `0px 2px 8px ${theme.colors.transparentDark[10]}`,
									backgroundColor: theme.colors.monochrome.white,
								}}
							>
								<Box
									flexDirection="row"
									justifyContent="space-between"
									width={'100%'}
								>
									<Box flexDirection="row" maxWidth={'100%'} gap={'15px'}>
										<Icon
											icon={IconCodes.icon_Tb_user}
											color={theme.colors.monochrome.label}
											fontSize={theme.fontSizes[DeviceTypes.Desktop].hSmall}
											iconStyle={{
												height: '100%',
											}}
										/>
										<Box flexDirection="column">
											<Text
												text={spanItem.input?.parts[0]?.text}
												textOverflow={'ellipsis'}
												maxWidth={'70%'}
												whiteSpace={'nowrap'}
												overflow={'hidden'}
											/>
											<Text
												text={new Date(spanItem.time_ms)
													.toString()
													.substring(4, 24)}
												textOverflow={'ellipsis'}
												width={'100%'}
												whiteSpace={'nowrap'}
												overflow={'hidden'}
												marginLeft={'20px'}
											/>
										</Box>
									</Box>
									<Box>
										<Text
											color={theme.colors.monochrome.body}
											fontSize={theme.fontSizes.desktop.bSmall}
										>
											{getRunTimeFromDuration(spanItem.time_ms)}
										</Text>
										<Icon
											{...getIconProps(false)}
											fontSize={theme.fontSizes.desktop.hXSmall}
										/>
									</Box>
								</Box>
								<Box
									flexDirection="row"
									justifyContent="space-between"
									width={'100%'}
								>
									<Box flexDirection="row" width={'100%'} gap={'10px'}>
										<Icon
											icon={IconCodes.icon_Tb_ai_agent}
											color={theme.colors.monochrome.label}
											fontSize={theme.fontSizes[DeviceTypes.Desktop].hXSmall}
											iconStyle={{
												height: '100%',
											}}
										/>
										<Box flexDirection="column" maxWidth={'100%'}>
											<Text
												text={spanItem.output?.parts[0]?.text}
												textOverflow={'ellipsis'}
												maxWidth={'70%'}
												whiteSpace={'nowrap'}
												overflow={'hidden'}
											/>
										</Box>
									</Box>
									<Box></Box>
								</Box>
							</Box>
						);
					})}
				</Scrollbar>
			);
		} else if (
			activeContent.content ===
			ActiveConvesationLogContent.SingleConversationLog
		) {
			return (
				<SingleConversationLogs
					data={activeContent.logData}
					handleBackButtonClick={handleBackButtonClick}
				/>
			);
		} else {
			return <></>;
		}
	};

	return <>{renderContent()}</>;
};
