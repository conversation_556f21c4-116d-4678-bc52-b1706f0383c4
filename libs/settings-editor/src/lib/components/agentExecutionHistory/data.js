export const data = {
	started_at: '2025-07-07T14:52:02.866508',
	time_ms: 1621291,
	has_error: false,
	has_error_child: false,
	label: 'session',
	input: null,
	output: null,
	spans: [
		{
			started_at: '2025-07-07T14:52:02.866508',
			time_ms: 7126,
			has_error: false,
			has_error_child: false,
			label: 'chat',
			input: {
				role: 'user',
				parts: [
					{
						text: 'hi',
					},
				],
			},
			output: {
				role: 'model',
				parts: [
					{
						text: 'The sentiment of "hi" is neutral.',
					},
				],
			},
			spans: [
				{
					started_at: '2025-07-07T14:52:02.881861',
					time_ms: 7094,
					has_error: false,
					has_error_child: false,
					label: 'data_classification_flow',
					input: {
						params: {
							arguments: {
								labels: ['happy', 'sad', 'neutral', 'chaotic'],
								content: 'hi',
							},
						},
					},
					output: {
						result: 'neutral',
						workflowId: 'Jiffy_1997309996',
					},
					error: null,
					spans: [],
				},
			],
		},
		{
			started_at: '2025-07-07T14:56:03.139611',
			time_ms: 7083,
			has_error: false,
			has_error_child: false,
			label: 'chat',
			input: {
				role: 'user',
				parts: [
					{
						text: 'hi',
					},
				],
			},
			output: {
				role: 'model',
				parts: [
					{
						text: 'The sentiment of "hi" is neutral.',
					},
				],
			},
			spans: [
				{
					started_at: '2025-07-07T14:56:03.157299',
					time_ms: 7050,
					has_error: false,
					has_error_child: false,
					label: 'data_classification_flow',
					input: {
						params: {
							arguments: {
								labels: ['happy', 'sad', 'neutral', 'chaotic'],
								content: 'hi',
							},
						},
					},
					output: {
						result: 'neutral',
						workflowId: 'Jiffy_1710612503',
					},
					error: null,
					spans: [],
				},
			],
		},
		{
			started_at: '2025-07-07T14:56:39.793824',
			time_ms: 6324,
			has_error: false,
			has_error_child: false,
			label: 'chat',
			input: {
				role: 'user',
				parts: [
					{
						text: 'wow its sunday',
					},
				],
			},
			output: {
				role: 'model',
				parts: [
					{
						text: 'The sentiment of "wow its sunday" is neutral.',
					},
				],
			},
			spans: [
				{
					started_at: '2025-07-07T14:56:39.813334',
					time_ms: 6287,
					has_error: false,
					has_error_child: false,
					label: 'data_classification_flow',
					input: {
						params: {
							arguments: {
								labels: ['happy', 'sad', 'neutral', 'chaotic'],
								content: 'wow its sunday',
							},
						},
					},
					output: {
						result: 'neutral',
						workflowId: 'Jiffy_905631372',
					},
					error: null,
					spans: [],
				},
			],
		},
		{
			started_at: '2025-07-07T14:56:59.089108',
			time_ms: 6329,
			has_error: false,
			has_error_child: false,
			label: 'chat',
			input: {
				role: 'user',
				parts: [
					{
						text: 'can you try again the same ?',
					},
				],
			},
			output: {
				role: 'model',
				parts: [
					{
						text: 'The sentiment of "wow its sunday" is neutral.',
					},
				],
			},
			spans: [
				{
					started_at: '2025-07-07T14:56:59.103513',
					time_ms: 6298,
					has_error: false,
					has_error_child: false,
					label: 'data_classification_flow',
					input: {
						params: {
							arguments: {
								labels: ['happy', 'sad', 'neutral', 'chaotic'],
								content: 'wow its sunday',
							},
						},
					},
					output: {
						result: 'neutral',
						workflowId: 'Jiffy_853571866',
					},
					error: null,
					spans: [],
				},
			],
		},
		{
			started_at: '2025-07-07T14:57:25.741500',
			time_ms: 16,
			has_error: false,
			has_error_child: false,
			label: 'chat',
			input: {
				role: 'user',
				parts: [
					{
						text: 'how many statements we classified now ?',
					},
				],
			},
			output: {
				role: 'model',
				parts: [
					{
						text: "I can classify statements for you, but I don't keep a count of how many statements have been classified. Each time, I process the new input provided.",
					},
				],
			},
			spans: [],
		},
		{
			started_at: '2025-07-07T14:59:53.638897',
			time_ms: 4953,
			has_error: false,
			has_error_child: false,
			label: 'chat',
			input: {
				role: 'user',
				parts: [
					{
						text: 'can you try again the same ? the sunday one',
					},
				],
			},
			output: {
				role: 'model',
				parts: [
					{
						text: 'The sentiment of "wow its sunday" is neutral.',
					},
				],
			},
			spans: [
				{
					started_at: '2025-07-07T14:59:53.657081',
					time_ms: 4918,
					has_error: false,
					has_error_child: false,
					label: 'data_classification_flow',
					input: {
						params: {
							arguments: {
								labels: ['happy', 'sad', 'neutral', 'chaotic'],
								content: 'wow its sunday',
							},
						},
					},
					output: {
						result: 'neutral',
						workflowId: 'Jiffy_1290531571',
					},
					error: null,
					spans: [],
				},
			],
		},
		{
			started_at: '2025-07-07T15:18:57.400005',
			time_ms: 6758,
			has_error: false,
			has_error_child: false,
			label: 'chat',
			input: {
				role: 'user',
				parts: [
					{
						text: 'what about saturday',
					},
				],
			},
			output: {
				role: 'model',
				parts: [
					{
						text: 'The sentiment of "what about saturday" is neutral.',
					},
				],
			},
			spans: [
				{
					started_at: '2025-07-07T15:18:57.417214',
					time_ms: 6720,
					has_error: false,
					has_error_child: false,
					label: 'data_classification_flow',
					input: {
						params: {
							arguments: {
								labels: ['happy', 'sad', 'neutral', 'chaotic'],
								content: 'what about saturday',
							},
						},
					},
					output: {
						result: 'neutral',
						workflowId: 'Jiffy_1024740877',
					},
					error: null,
					spans: [],
				},
			],
		},
	],
};
