import Box from '@atomic/box';
import Text from '@atomic/text';
import { ApexTheme, DeviceTypes } from '@base/theme';
import { useTheme } from 'react-jss';
import Icon, { Animations, IconCodes } from '@atomic/icon';
import { useCallback, useState } from 'react';
import { getRunTimeFromDuration } from './agentExecutionHistoryDetails';
import Info, { InfoTypes } from '@atomic/info';

export interface RunLogProps {
	span: any[];
}

export const RunLog: React.FC<RunLogProps> = ({ span }): JSX.Element => {
	const theme: ApexTheme = useTheme();
	const [isExpanded, setIsExpanded] = useState(false);

	const getIconProps = (hasError: Boolean) => {
		return !hasError
			? {
					icon: IconCodes.icon_Bd_Filled_Success,
					color: theme.colors.success.default,
			  }
			: {
					icon: IconCodes.icon_Bd_Filled_Close,
					color: theme.colors.red[500],
			  };
	};

	const handleToggleExpand = useCallback(() => {
		setIsExpanded(!isExpanded);
	}, []);

	return (
		<Box flexDirection="column" width={'100%'}>
			{span.map((spanItem: any, index: number) => {
				return (
					<>
						<Box
							justifyContent="space-between"
							padding={'0px 10px'}
							width={'100%'}
						>
							<Box>
								{spanItem.spans.length ? (
									<Icon
										onClick={handleToggleExpand}
										icon={
											isExpanded
												? IconCodes.icon_Bd_Arrow_Caret_Down
												: IconCodes.icon_Bd_Arrow_Caret_Forward
										}
										height={24}
										width={24}
										fontSize={theme.fontSizes[DeviceTypes.Desktop].hXSmall}
										color={theme.colors.monochrome.label}
									/>
								) : (
									<></>
								)}
								<Text
									color={theme.colors.monochrome.body}
									fontSize={theme.fontSizes.desktop.bSmall}
								>
									{spanItem.label}
								</Text>
							</Box>{' '}
							<Box width={'70px'} justifyContent="space-between">
								{spanItem.time_ms && (
									<Text
										color={theme.colors.monochrome.body}
										fontSize={theme.fontSizes.desktop.bSmall}
									>
										{getRunTimeFromDuration(spanItem.time_ms)}
									</Text>
								)}
								<Icon
									{...getIconProps(
										spanItem.has_error || spanItem.has_error_child
									)}
									fontSize={theme.fontSizes.desktop.hXSmall}
								/>
							</Box>
						</Box>
						{isExpanded && (
							<Box marginLeft={'20px'} width={'96%'}>
								<RunLog
									key={`${spanItem.label}${index}`}
									span={spanItem.spans}
								/>
							</Box>
						)}
					</>
				);
			})}
		</Box>
	);
};
