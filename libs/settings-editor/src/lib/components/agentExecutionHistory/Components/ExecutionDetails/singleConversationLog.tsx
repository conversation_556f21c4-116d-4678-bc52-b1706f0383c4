import Box from '@atomic/box';
import Text from '@atomic/text';
import { ApexTheme, DeviceTypes } from '@base/theme';
import { useTheme } from 'react-jss';
import Icon, { Animations, IconCodes } from '@atomic/icon';
import { useCallback, useState } from 'react';
import { getRunTimeFromDuration } from './agentExecutionHistoryDetails';
import Info, { InfoTypes } from '@atomic/info';
import { useScrollbarStyles } from '@atomic/scrollbar';
import { ActiveConvesationLogContent } from './constants';
import { RunLog } from './runLog';

export interface SingleConversationLogsProps {
	data: any;
    handleBackButtonClick: () => void;
}

export const SingleConversationLogs: React.FC<SingleConversationLogsProps> = ({
	data,
    handleBackButtonClick
}): JSX.Element => {
	const theme: ApexTheme = useTheme();
	console.log('data', data);

	const getIconProps = (hasError: Boolean) => {
		return !hasError
			? {
					icon: IconCodes.icon_Bd_Filled_Success,
					color: theme.colors.success.default,
			  }
			: {
					icon: IconCodes.icon_Bd_Filled_Close,
					color: theme.colors.red[500],
			  };
	};

	return (
		<Box flexDirection="column" gap={'15px'}>
			<Box flexDirection='row' gap={'10px'}>
				<Icon
					icon={IconCodes.icon_Tb_arrow_left}
					color={theme.colors.monochrome.label}
					fontSize={theme.fontSizes[DeviceTypes.Desktop].hXSmall}
					iconStyle={{
						height: '100%',
					}}
                    onClick={handleBackButtonClick}
				/>
                <Text
					text={'Conversation Log'}
					textOverflow={'ellipsis'}
					width={'93%'}
					maxWidth={'93%'}
                    fontWeight={theme.fontWeights.semiBold}
                    fontSize={theme.fontSizes[DeviceTypes.Desktop].bMedium}
					whiteSpace={'nowrap'}
					overflow={'hidden'}
                    alignSelf='center'
				/>
			</Box>
			<Box
				flexDirection="row"
				justifyContent="space-between"
				maxWidth={'100%'}
				borderBottom={`1px solid ${theme.colors.monochrome.input}`}
			>
				<Box flexDirection="row" maxWidth={'100%'} gap={'15px'}>
					<Icon
						icon={IconCodes.icon_Tb_user}
						color={theme.colors.monochrome.label}
						fontSize={theme.fontSizes[DeviceTypes.Desktop].hSmall}
						iconStyle={{
							height: '100%',
						}}
					/>
					<Box flexDirection="column">
						<Text
							text={data.input.parts[0].text}
							textOverflow={'ellipsis'}
							width={'93%'}
							maxWidth={'93%'}
							whiteSpace={'nowrap'}
							overflow={'hidden'}
						/>
						<Text
							text={new Date(data.time_ms).toString().substring(4, 24)}
							textOverflow={'ellipsis'}
							width={'93%'}
							whiteSpace={'nowrap'}
							overflow={'hidden'}
							marginLeft={'20px'}
						/>
					</Box>
				</Box>
				<Box>
					<Text
						color={theme.colors.monochrome.body}
						fontSize={theme.fontSizes.desktop.bSmall}
					>
						{getRunTimeFromDuration(data.time_ms)}
					</Text>
					<Icon
						{...getIconProps(false)}
						fontSize={theme.fontSizes.desktop.hXSmall}
					/>
				</Box>
			</Box>
			<Box
				marginLeft={'33px'}
				borderBottom={`1px solid ${theme.colors.monochrome.input}`}
			>
				<RunLog key={`1`} span={data.spans} />
			</Box>
			<Box
				flexDirection="row"
				justifyContent="space-between"
				maxWidth={'100%'}
				borderBottom={`1px solid ${theme.colors.monochrome.input}`}
			>
				<Box flexDirection="row" width={'100%'} gap={'10px'}>
					<Icon
						icon={IconCodes.icon_Tb_ai_agent}
						color={theme.colors.monochrome.label}
						fontSize={theme.fontSizes[DeviceTypes.Desktop].hXSmall}
						iconStyle={{
							height: '100%',
						}}
					/>
					<Box flexDirection="column" maxWidth={'100%'}>
						<Text
							text={data.output.parts[0].text}
							textOverflow={'ellipsis'}
							width={'100%'}
							whiteSpace={'nowrap'}
							overflow={'hidden'}
						/>
						{/* <Text
							text={new Date(data.started_at).toString().substring(4, 24)}
							textOverflow={'ellipsis'}
							width={'93%'}
							whiteSpace={'nowrap'}
							overflow={'hidden'}
							marginLeft={'20px'}
						/> */}
					</Box>
				</Box>
				<Box>
					{/* <Text
						color={theme.colors.monochrome.body}
						fontSize={theme.fontSizes.desktop.bSmall}
					>
						{getRunTimeFromDuration(data.time_ms)}
					</Text>
					<Icon
						{...getIconProps(false)}
						fontSize={theme.fontSizes.desktop.hXSmall}
					/> */}
				</Box>
			</Box>
		</Box>
	);
};
